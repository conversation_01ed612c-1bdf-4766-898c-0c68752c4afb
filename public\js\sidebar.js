// Responsive Sidebar Component - Arabic RTL

class ResponsiveSidebar {
  constructor() {
    this.sidebar = null;
    this.overlay = null;
    this.toggleBtn = null;
    this.mobileToggleBtn = null;
    this.isOpen = false;
    this.isCollapsed = false;
    this.isMobile = window.innerWidth < 1200;
    
    this.init();
  }

  init() {
    this.createSidebar();
    this.setupEventListeners();
    this.updateResponsive();
  }

  createSidebar() {
    // Create sidebar overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'sidebar-overlay';
    document.body.appendChild(this.overlay);

    // Create mobile toggle button
    this.mobileToggleBtn = document.createElement('button');
    this.mobileToggleBtn.className = 'mobile-sidebar-toggle';
    this.mobileToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
    document.body.appendChild(this.mobileToggleBtn);

    // Create sidebar
    this.sidebar = document.createElement('div');
    this.sidebar.className = 'sidebar';
    this.sidebar.innerHTML = this.getSidebarHTML();
    document.body.appendChild(this.sidebar);

    // Get toggle button reference
    this.toggleBtn = this.sidebar.querySelector('.sidebar-toggle');
  }

  getSidebarHTML() {
    const currentPage = window.location.pathname;
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const isAuthenticated = !!localStorage.getItem('token');

    return `
      <div class="sidebar-header">
        <a href="/" class="sidebar-brand">
          <i class="fas fa-graduation-cap"></i>
          <span class="sidebar-brand-text">منصة دوراتي</span>
        </a>
        <button class="sidebar-toggle">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>

      <nav class="sidebar-nav">
        <!-- Main Navigation -->
        <div class="sidebar-nav-section">
          <div class="sidebar-nav-title">التنقل الرئيسي</div>
          <ul class="sidebar-nav-list">
            <li class="sidebar-nav-item">
              <a href="/" class="sidebar-nav-link ${currentPage === '/' || currentPage === '/index.html' ? 'active' : ''}">
                <i class="fas fa-home sidebar-nav-icon"></i>
                <span class="sidebar-nav-text">الرئيسية</span>
              </a>
            </li>
            <li class="sidebar-nav-item">
              <a href="/courses.html" class="sidebar-nav-link ${currentPage === '/courses.html' ? 'active' : ''}">
                <i class="fas fa-book sidebar-nav-icon"></i>
                <span class="sidebar-nav-text">الدورات</span>
              </a>
            </li>
            ${isAuthenticated ? `
              <li class="sidebar-nav-item">
                <a href="/profile.html?access=allowed&role=${user.role}" class="sidebar-nav-link ${currentPage === '/profile.html' ? 'active' : ''}">
                  <i class="fas fa-user sidebar-nav-icon"></i>
                  <span class="sidebar-nav-text">الملف الشخصي</span>
                </a>
              </li>
            ` : ''}
            ${isAuthenticated && user.role === 'admin' ? `
              <li class="sidebar-nav-item">
                <a href="/admin-dashboard.html" class="sidebar-nav-link ${currentPage === '/admin-dashboard.html' ? 'active' : ''}">
                  <i class="fas fa-tachometer-alt sidebar-nav-icon"></i>
                  <span class="sidebar-nav-text">لوحة التحكم</span>
                </a>
              </li>
            ` : ''}
          </ul>
        </div>

        <!-- Information -->
        <div class="sidebar-nav-section">
          <div class="sidebar-nav-title">معلومات</div>
          <ul class="sidebar-nav-list">
            <li class="sidebar-nav-item">
              <a href="#about" class="sidebar-nav-link">
                <i class="fas fa-info-circle sidebar-nav-icon"></i>
                <span class="sidebar-nav-text">من نحن</span>
              </a>
            </li>
            <li class="sidebar-nav-item">
              <a href="#contact" class="sidebar-nav-link">
                <i class="fas fa-envelope sidebar-nav-icon"></i>
                <span class="sidebar-nav-text">تواصل معنا</span>
              </a>
            </li>
            <li class="sidebar-nav-item">
              <a href="#help" class="sidebar-nav-link">
                <i class="fas fa-question-circle sidebar-nav-icon"></i>
                <span class="sidebar-nav-text">المساعدة</span>
              </a>
            </li>
          </ul>
        </div>

        ${!isAuthenticated ? `
          <!-- Authentication -->
          <div class="sidebar-nav-section">
            <div class="sidebar-nav-title">الحساب</div>
            <ul class="sidebar-nav-list">
              <li class="sidebar-nav-item">
                <a href="/login.html" class="sidebar-nav-link">
                  <i class="fas fa-sign-in-alt sidebar-nav-icon"></i>
                  <span class="sidebar-nav-text">تسجيل الدخول</span>
                </a>
              </li>
              <li class="sidebar-nav-item">
                <a href="/register.html" class="sidebar-nav-link">
                  <i class="fas fa-user-plus sidebar-nav-icon"></i>
                  <span class="sidebar-nav-text">إنشاء حساب</span>
                </a>
              </li>
            </ul>
          </div>
        ` : ''}
      </nav>

      ${isAuthenticated ? `
        <div class="sidebar-footer">
          <div class="sidebar-user">
            <img src="${user.profileImage || '/uploads/no-photo.jpg'}" alt="الصورة الشخصية" class="sidebar-user-avatar">
            <div class="sidebar-user-info">
              <span class="sidebar-user-name">${user.name || 'مستخدم'}</span>
              <span class="sidebar-user-role">${user.role === 'admin' ? 'مدير' : 'مستخدم'}</span>
            </div>
          </div>
        </div>
      ` : ''}
    `;
  }

  setupEventListeners() {
    // Toggle button click
    if (this.toggleBtn) {
      this.toggleBtn.addEventListener('click', () => this.toggleCollapse());
    }

    // Mobile toggle button click
    this.mobileToggleBtn.addEventListener('click', () => this.toggleSidebar());

    // Overlay click
    this.overlay.addEventListener('click', () => this.closeSidebar());

    // Window resize
    window.addEventListener('resize', () => this.handleResize());

    // Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen && this.isMobile) {
        this.closeSidebar();
      }
    });

    // Update sidebar when auth state changes
    window.addEventListener('authStateChanged', () => {
      this.updateSidebar();
    });
  }

  toggleSidebar() {
    if (this.isOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    this.isOpen = true;
    this.sidebar.classList.add('open');
    this.overlay.classList.add('active');
    this.mobileToggleBtn.innerHTML = '<i class="fas fa-times"></i>';
    document.body.style.overflow = 'hidden';
  }

  closeSidebar() {
    this.isOpen = false;
    this.sidebar.classList.remove('open');
    this.overlay.classList.remove('active');
    this.mobileToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
    document.body.style.overflow = '';
  }

  toggleCollapse() {
    if (this.isMobile) return;
    
    this.isCollapsed = !this.isCollapsed;
    this.sidebar.classList.toggle('collapsed', this.isCollapsed);
    
    const content = document.querySelector('.content-with-sidebar');
    if (content) {
      content.classList.toggle('sidebar-collapsed', this.isCollapsed);
    }

    // Update toggle icon
    const icon = this.toggleBtn.querySelector('i');
    icon.className = this.isCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right';
  }

  handleResize() {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth < 1200;

    if (wasMobile !== this.isMobile) {
      this.updateResponsive();
    }
  }

  updateResponsive() {
    if (this.isMobile) {
      this.closeSidebar();
      this.sidebar.classList.remove('collapsed');
      this.isCollapsed = false;
    } else {
      this.sidebar.classList.add('open');
      this.overlay.classList.remove('active');
      document.body.style.overflow = '';
    }

    this.updateContentMargin();
  }

  updateContentMargin() {
    const content = document.querySelector('.content-with-sidebar');
    if (!content) return;

    if (this.isMobile) {
      content.style.marginRight = '0';
    } else {
      content.style.marginRight = this.isCollapsed ? 
        'var(--sidebar-collapsed-width)' : 
        'var(--sidebar-width)';
    }
  }

  updateSidebar() {
    // Refresh sidebar content when auth state changes
    this.sidebar.innerHTML = this.getSidebarHTML();
    this.toggleBtn = this.sidebar.querySelector('.sidebar-toggle');
    
    // Re-setup toggle button event listener
    if (this.toggleBtn) {
      this.toggleBtn.addEventListener('click', () => this.toggleCollapse());
    }
  }

  // Public method to refresh sidebar
  refresh() {
    this.updateSidebar();
  }
}

// Initialize sidebar when DOM is loaded
let sidebarInstance = null;

document.addEventListener('DOMContentLoaded', () => {
  sidebarInstance = new ResponsiveSidebar();
});

// Export for use in other scripts
window.ResponsiveSidebar = ResponsiveSidebar;
window.sidebarInstance = sidebarInstance;
