// ========== Notification styles (animations) ==========
(function () {
    const notificationStyles = document.createElement("style");
    notificationStyles.textContent = `
      @keyframes slideInDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
      }
      @keyframes slideOutUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
      }
    `;
    document.head.appendChild(notificationStyles);
  })();
  
  // ========== Notification System ==========
  function showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <i class="fas fa-${
        type === "success"
          ? "check-circle"
          : type === "error"
          ? "exclamation-circle"
          : "info-circle"
      }"></i>
      <span>${message}</span>
      <button onclick="this.parentElement.remove()"><i class="fas fa-times"></i></button>
    `;
    notification.style.cssText = `
      position: fixed;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--white);
      color: var(--gray-800);
      padding: 1rem 1.5rem;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-xl);
      border-left: 4px solid var(--${
        type === "success" ? "success" : type === "error" ? "error" : "info"
      }-color);
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      max-width: 400px;
      animation: slideInDown 0.3s ease-out;
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      notification.style.animation = "slideOutUp 0.3s ease-out";
      setTimeout(() => notification.remove(), 300);
    }, 5000);
  }
  
  // ========== Mobile menu toggle ==========
  (function () {
    const mobileToggle = document.querySelector(".mobile-toggle");
    const navLinks = document.querySelector(".nav-links");
    if (mobileToggle && navLinks) {
      mobileToggle.addEventListener("click", () => {
        navLinks.classList.toggle("active");
        mobileToggle.classList.toggle("active");
        if (navLinks.classList.contains("active")) {
          navLinks.style.display = "flex";
          navLinks.style.flexDirection = "column";
          navLinks.style.position = "absolute";
          navLinks.style.top = "60px";
          navLinks.style.right = "0";
          navLinks.style.background = "rgba(255,255,255,0.98)";
          navLinks.style.width = "100%";
          navLinks.style.boxShadow = "var(--shadow-lg)";
          navLinks.style.zIndex = "1001";
          navLinks.style.padding = "1rem 0";
        } else {
          navLinks.removeAttribute("style");
        }
      });
    }
  })();
  
  // ========== Password toggle ==========
  function togglePassword() {
    const passwordInput = document.getElementById("password");
    const toggleButton = document.querySelector(".password-toggle i");
    if (passwordInput.type === "password") {
      passwordInput.type = "text";
      toggleButton.classList.remove("fa-eye");
      toggleButton.classList.add("fa-eye-slash");
    } else {
      passwordInput.type = "password";
      toggleButton.classList.remove("fa-eye-slash");
      toggleButton.classList.add("fa-eye");
    }
  }
  document
    .getElementById("togglePasswordBtn")
    .addEventListener("click", togglePassword);
  
  // ========== Social login handlers ==========
  document.getElementById("google-login").addEventListener("click", function (e) {
    e.preventDefault();
    showNotification("جاري تسجيل الدخول عبر Google...", "info");
  });
  document
    .getElementById("facebook-login")
    .addEventListener("click", function (e) {
      e.preventDefault();
      showNotification("جاري تسجيل الدخول عبر Facebook...", "info");
    });
  
  // ========== Enhanced form animations ==========
  document.querySelectorAll(".form-control").forEach((input) => {
    input.addEventListener("focus", function () {
      this.parentElement.style.transform = "scale(1.02)";
      this.parentElement.style.transition = "var(--transition)";
    });
    input.addEventListener("blur", function () {
      this.parentElement.style.transform = "scale(1)";
    });
  });
  
  // ========== Keyboard navigation ==========
  document.addEventListener("keydown", function (e) {
    if (
      e.key === "Enter" &&
      e.target.tagName !== "BUTTON" &&
      e.target.type !== "submit"
    ) {
      const form = e.target.closest("form");
      if (form) {
        const inputs = Array.from(
          form.querySelectorAll("input, select, textarea")
        );
        const currentIndex = inputs.indexOf(e.target);
        const nextIndex = currentIndex + 1;
        if (nextIndex < inputs.length) {
          inputs[nextIndex].focus();
        } else {
          form.querySelector('button[type="submit"]').click();
        }
      }
    }
  });
  
  // ========== Welcome message ==========
  window.addEventListener("load", function () {
    setTimeout(() => {
      showNotification("أهلاً وسهلاً بك في منصة دوراتي! 🎓", "info");
    }, 1000);
  });
  
  // ========== Auth Helper functions ==========
  function setAuthData(token, user) {
    localStorage.setItem("token", token);
    localStorage.setItem("user", JSON.stringify(user));
    
  }
  function getCurrentUser() {
    const user = localStorage.getItem("authUser");
    return user ? JSON.parse(user) : null;
  }
  function isLoggedIn() {
    return !!localStorage.getItem("authToken");
  }
  
  // ========== Validation ==========
  function validateForm() {
    const email = document.getElementById("email");
    const password = document.getElementById("password");
    let isValid = true;
  
    // clear errors
    document.querySelectorAll(".error-message").forEach((el) => {
      el.style.display = "none";
      el.textContent = "";
    });
    document.querySelectorAll(".form-control").forEach((el) => {
      el.classList.remove("is-invalid", "is-valid");
    });
  
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email.value.trim()) {
      showError("email", "البريد الإلكتروني مطلوب");
      isValid = false;
    } else if (!emailRegex.test(email.value)) {
      showError("email", "يرجى إدخال بريد إلكتروني صحيح");
      isValid = false;
    } else {
      email.classList.add("is-valid");
    }
  
    if (!password.value.trim()) {
      showError("password", "كلمة المرور مطلوبة");
      isValid = false;
    } else if (password.value.length < 6) {
      showError("password", "كلمة المرور يجب أن تكون 6 أحرف على الأقل");
      isValid = false;
    } else {
      password.classList.add("is-valid");
    }
  
    return isValid;
  }
  function showError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorDiv = document.getElementById(fieldId + "-error");
    field.classList.add("is-invalid");
    errorDiv.textContent = message;
    errorDiv.style.display = "block";
  }
  
  // ========== Login API Submission ==========
  document
    .getElementById("loginForm")
    .addEventListener("submit", async function (e) {
      e.preventDefault();
      if (!validateForm()) return;
  
      const submitBtn = document.querySelector(".btn-auth");
      const btnText = document.querySelector(".btn-text");
  
      submitBtn.disabled = true;
      btnText.innerHTML =
        '<div class="loading"><div class="spinner"></div><span>جاري تسجيل الدخول...</span></div>';
  
      try {
        const loginData = {
          email: document.getElementById("email").value,
          password: document.getElementById("password").value,
        };
  
        const response = await fetch("http://localhost:4009/api/auth/login", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(loginData),
        });
        const data = await response.json();
        console.log("API response:", data); // Debug
  
        if (!data.success) {
          throw new Error(data.message || "فشل تسجيل الدخول");
        }
  
        // user object could be in data.data or data.user
        const user = data.data || data.user;
  
        // Save token + user
        setAuthData(data.token, user);
  
        // Success
        btnText.innerHTML =
          '<i class="fas fa-check"></i> تم تسجيل الدخول بنجاح!';
        submitBtn.style.background = "var(--success-color)";
        showNotification("تم تسجيل الدخول بنجاح! جاري التوجيه...", "success");
  
        setTimeout(() => {
          if (user.role === "admin") {
            window.location.href = "/admin-dashboard.html";
          } else {
            window.location.href = "/courses.html";
          }
        }, 2000);
      } catch (error) {
        console.error("Login error:", error);
        showNotification(error.message, "error");
        btnText.textContent = "تسجيل الدخول";
      } finally {
        submitBtn.disabled = false;
      }
    });
  
  // ========== Real-time validation ==========
  document.getElementById("email").addEventListener("blur", function () {
    if (this.value.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(this.value)) {
        this.classList.remove("is-invalid");
        this.classList.add("is-valid");
        document.getElementById("email-error").style.display = "none";
      }
    }
  });
  document.getElementById("password").addEventListener("blur", function () {
    if (this.value.trim() && this.value.length >= 6) {
      this.classList.remove("is-invalid");
      this.classList.add("is-valid");
      document.getElementById("password-error").style.display = "none";
    }
  });
  