const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:4009';
const TEST_ADMIN = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Create a simple test video file (WebM format which is easier to create)
function createTestVideoFile() {
  const videoPath = path.join(__dirname, 'test-upload-video.webm');
  
  // Create a minimal WebM file header for testing
  const webmHeader = Buffer.from([
    0x1A, 0x45, 0xDF, 0xA3, // EBML header
    0x9F, 0x42, 0x86, 0x81, 0x01,
    0x42, 0x82, 0x84, 0x77, 0x65, 0x62, 0x6D, // webm
    0x42, 0x87, 0x81, 0x02,
    0x42, 0x85, 0x81, 0x02
  ]);
  
  try {
    fs.writeFileSync(videoPath, webmHeader);
    console.log('📹 Test video file created:', videoPath);
    return videoPath;
  } catch (error) {
    console.error('❌ Failed to create test video:', error.message);
    return null;
  }
}

// Login and get token
async function loginAdmin() {
  try {
    console.log('🔐 Logging in as admin...');
    const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_ADMIN);
    
    if (response.data.success && response.data.token) {
      console.log('✅ Admin login successful');
      return response.data.token;
    } else {
      throw new Error('Login failed - no token received');
    }
  } catch (error) {
    console.error('❌ Admin login failed:', error.response?.data?.message || error.message);
    return null;
  }
}

// Create a test course
async function createTestCourse(token) {
  try {
    console.log('📚 Creating test course...');
    const courseData = {
      title: 'Test Course for Video Upload',
      description: 'This is a test course for video upload testing',
      instructor: 'Test Instructor',
      category: 'development',
      level: 'beginner',
      price: 0,
      isPublished: true
    };

    const response = await axios.post(`${BASE_URL}/api/courses`, courseData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success && response.data.data) {
      console.log('✅ Test course created:', response.data.data._id);
      return response.data.data._id;
    } else {
      throw new Error('Course creation failed');
    }
  } catch (error) {
    console.error('❌ Course creation failed:', error.response?.data?.message || error.message);
    return null;
  }
}

// Upload video to course
async function uploadVideoToCourse(token, courseId, videoPath) {
  try {
    console.log('🎬 Uploading video to course...');
    
    const formData = new FormData();
    formData.append('title', 'Test Video');
    formData.append('description', 'This is a test video upload');
    formData.append('course', courseId);
    formData.append('order', '1');
    formData.append('isPublished', 'true');
    formData.append('video', fs.createReadStream(videoPath));

    const response = await axios.post(`${BASE_URL}/api/courses/${courseId}/videos`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      timeout: 60000 // 60 seconds timeout for video upload
    });

    if (response.data.success && response.data.data) {
      console.log('✅ Video uploaded successfully:');
      console.log('- Video ID:', response.data.data._id);
      console.log('- Video URL:', response.data.data.videoUrl);
      console.log('- Duration:', response.data.data.duration);
      return response.data.data;
    } else {
      throw new Error('Video upload failed');
    }
  } catch (error) {
    console.error('❌ Video upload failed:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
    return null;
  }
}

// Main test function
async function runVideoUploadTest() {
  console.log('🚀 Starting video upload test...\n');
  
  // Create test video file
  const videoPath = createTestVideoFile();
  if (!videoPath) {
    console.log('❌ Cannot proceed without test video file');
    return;
  }

  try {
    // Step 1: Login
    const token = await loginAdmin();
    if (!token) {
      console.log('❌ Cannot proceed without admin token');
      return;
    }

    // Step 2: Create test course
    const courseId = await createTestCourse(token);
    if (!courseId) {
      console.log('❌ Cannot proceed without test course');
      return;
    }

    // Step 3: Upload video
    const video = await uploadVideoToCourse(token, courseId, videoPath);
    
    if (video) {
      console.log('\n🎉 Video upload test completed successfully!');
      console.log('You can now check the video in the admin dashboard.');
    } else {
      console.log('\n⚠️ Video upload test failed.');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    // Clean up test video file
    if (fs.existsSync(videoPath)) {
      fs.unlinkSync(videoPath);
      console.log('🧹 Test video file cleaned up');
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  runVideoUploadTest().catch(console.error);
}

module.exports = {
  runVideoUploadTest,
  createTestVideoFile,
  loginAdmin,
  createTestCourse,
  uploadVideoToCourse
};
