    /* Enhanced Dark Theme Variables */
    :root {
      --primary-color: #6366f1;
      --primary-dark: #4f46e5;
      --primary-light: #8b5cf6;
      --secondary-color: #10b981;
      --accent-color: #f59e0b;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --info-color: #3b82f6;

      --dark-bg: #0f0f23;
      --dark-surface: #1a1a2e;
      --dark-card: #16213e;
      --dark-hover: #0e3460;
      --dark-border: #2d3748;
      --dark-text: #e2e8f0;
      --dark-text-muted: #94a3b8;
      --dark-text-light: #cbd5e0;

      --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --font-size-xs: 0.75rem;
      --font-size-sm: 0.875rem;
      --font-size-base: 1rem;
      --font-size-lg: 1.125rem;
      --font-size-xl: 1.25rem;
      --font-size-2xl: 1.5rem;
      --font-size-3xl: 1.875rem;
      --font-size-4xl: 2.25rem;

      --border-radius: 0.75rem;
      --border-radius-lg: 1rem;
      --border-radius-xl: 1.25rem;

      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
      --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.3);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.4);

      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--dark-text);
      direction: rtl;
      min-height: 100vh;
      padding-top: 80px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    /* Navigation */
    .navbar {
      background: rgba(26, 26, 46, 0.95);
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-lg);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 1rem 0;
      border-bottom: 1px solid var(--dark-border);
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--primary-color);
      text-decoration: none;
      transition: var(--transition);
    }

    .navbar-brand:hover {
      color: var(--primary-light);
      transform: scale(1.05);
    }

    .navbar-brand i {
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-link {
      color: var(--dark-text-light);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
      position: relative;
    }

    .nav-link:hover,
    .nav-link.active {
      color: var(--primary-color);
      background: rgba(99, 102, 241, 0.1);
    }

    .nav-cta {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-menu {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-name {
      color: var(--dark-text);
      font-weight: 600;
      font-size: var(--font-size-base);
    }

    .mobile-toggle {
      display: none;
      flex-direction: column;
      cursor: pointer;
      gap: 0.25rem;
    }

    .mobile-toggle span {
      width: 25px;
      height: 3px;
      background: var(--dark-text);
      border-radius: 2px;
      transition: var(--transition);
    }

    /* Buttons */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      font-size: var(--font-size-base);
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: var(--transition);
      white-space: nowrap;
      font-family: inherit;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      filter: brightness(1.1);
    }

    .btn-secondary {
      background: var(--dark-card);
      color: var(--dark-text);
      border: 1px solid var(--dark-border);
    }

    .btn-secondary:hover {
      background: var(--dark-hover);
      border-color: var(--primary-color);
    }

    .btn-outline {
      background: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-outline:hover {
      background: var(--primary-color);
      color: white;
    }

    .btn-large {
      padding: 1.25rem 2rem;
      font-size: var(--font-size-lg);
    }

    /* Breadcrumb */
    .breadcrumb {
      padding: 1.5rem 0;
      color: var(--dark-text-muted);
      font-size: var(--font-size-base);
      background:#0f0f23;
      border-bottom: 1px solid var(--dark-border);
    }

    .breadcrumb a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
    }

    .breadcrumb a:hover {
      color: var(--primary-light);
      text-decoration: underline;
    }

    .breadcrumb span {
      margin: 0 0.75rem;
      color: var(--dark-text-muted);
    }

    /* Course Header */
    .course-header {
      background:#0f0f23 ;
      color: white;
      padding: 3rem 0 5rem;
      position: relative;
      overflow: hidden;
    }

    .course-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #0f0f23 ;
      opacity: 0.5;
    }

    .course-header-info {
      position: relative;
      z-index: 1;
      max-width: 800px;
    }

    .course-category {
      background: rgba(255, 255, 255, 0.568);
      display: inline-block;
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-base);
      font-weight: 600;
      margin-bottom: 1.5rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .course-title {
      font-size: var(--font-size-4xl);
      font-weight: 800;
      margin-bottom: 1.5rem;
      line-height: 1.2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .course-subtitle {
      font-size: var(--font-size-xl);
      opacity: 0.95;
      margin-bottom: 2.5rem;
      line-height: 1.6;
      font-weight: 400;
    }

    .course-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 2.5rem;
      margin-bottom: 2rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: var(--font-size-base);
      font-weight: 500;
      background: rgba(255, 255, 255, 0.1);
      padding: 0.75rem 1.25rem;
      border-radius: var(--border-radius);
      backdrop-filter: blur(10px);
    }

    .meta-item i {
      color: rgba(255, 255, 255, 0.9);
      font-size: var(--font-size-lg);
    }

    .course-instructor-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: var(--font-size-lg);
      font-weight: 600;
      background: rgba(255, 255, 255, 0.15);
      padding: 1rem 1.5rem;
      border-radius: var(--border-radius);
      backdrop-filter: blur(10px);
      display: inline-flex;
    }

    .course-instructor-info i {
      font-size: var(--font-size-xl);
    }

    /* Course Content */
    .course-content {
      padding: 4rem 0;
    }

    .course-layout {
      display: grid;
      grid-template-columns: 1fr 380px;
      gap: 4rem;
      align-items: start;
    }

    /* Course Main */
    .course-main {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--dark-border);
    }

    /* Course Tabs */
    .course-tabs {
      display: flex;
      border-bottom: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .tab-btn {
      flex: 1;
      padding: 1.25rem 1.5rem;
      border: none;
      background: transparent;
      color: var(--dark-text-muted);
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      font-size: var(--font-size-base);
      font-family: inherit;
    }

    .tab-btn:hover {
      background: var(--dark-hover);
      color: var(--dark-text);
    }

    .tab-btn.active {
      background: var(--dark-card);
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }

    /* Tab Content */
    .tab-content {
      padding: 2.5rem;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
      animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Course Description */
    .course-description h3 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .course-description p {
      color: var(--dark-text-light);
      line-height: 1.8;
      margin-bottom: 2rem;
      font-size: var(--font-size-lg);
    }

    .course-tags {
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid var(--dark-border);
    }

    .course-tags h4 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
      font-weight: 600;
    }

    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
    }

    .tag {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-sm);
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      transition: var(--transition);
    }

    .tag:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    /* Course Features */
    .course-features {
      margin-top: 3rem;
    }

    .course-features h3 {
      color: var(--dark-text);
      margin-bottom: 2rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: var(--dark-surface);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--dark-border);
      transition: var(--transition);
    }

    .feature-item:hover {
      background: var(--dark-hover);
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .feature-item i {
      color: var(--success-color);
      font-size: var(--font-size-xl);
      background: rgba(16, 185, 129, 0.1);
      padding: 0.75rem;
      border-radius: var(--border-radius);
    }

    .feature-item span {
      font-weight: 500;
      color: var(--dark-text-light);
      font-size: var(--font-size-base);
    }

    /* Curriculum */
    .curriculum-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2.5rem;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .curriculum-header h3 {
      color: var(--dark-text);
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .curriculum-stats {
      display: flex;
      gap: 3rem;
    }

    .stat {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: var(--font-size-2xl);
      font-weight: 800;
      color: var(--primary-color);
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: var(--font-size-base);
      color: var(--dark-text-muted);
      font-weight: 500;
    }

    .curriculum-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .curriculum-item {
      border: 1px solid var(--dark-border);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      transition: var(--transition);
      cursor: pointer;
      background: var(--dark-surface);
      box-shadow: var(--shadow-sm);
    }

    .curriculum-item:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }

    .curriculum-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      background: var(--dark-card);
    }

    .item-info {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      flex: 1;
    }

    .video-thumbnail {
      width: 100px;
      height: 75px;
      border-radius: var(--border-radius-lg);
      overflow: hidden;
      background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      flex-shrink: 0;
      border: 2px solid var(--dark-border);
      transition: var(--transition);
    }

    .curriculum-item:hover .video-thumbnail {
      border-color: var(--primary-color);
      transform: scale(1.05);
    }

    .video-thumb-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: var(--transition);
    }

    .video-thumb-placeholder {
      color: var(--primary-color);
      font-size: var(--font-size-2xl);
      background: rgba(99, 102, 241, 0.1);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .video-details {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      flex: 1;
    }

    .item-title {
      font-weight: 700;
      color: var(--dark-text);
      font-size: var(--font-size-xl);
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .video-number {
      font-size: var(--font-size-sm);
      color: var(--primary-color);
      font-weight: 600;
      background: rgba(99, 102, 241, 0.1);
      padding: 0.25rem 0.75rem;
      border-radius: var(--border-radius);
      display: inline-block;
      width: fit-content;
      border: 1px solid rgba(99, 102, 241, 0.2);
    }

    .item-duration {
      color: var(--dark-text);
      font-size: var(--font-size-base);
      font-weight: 700;
      background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
      padding: 0.75rem 1.25rem;
      border-radius: var(--border-radius-lg);
      border: 2px solid var(--dark-border);
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: var(--transition);
      box-shadow: var(--shadow-sm);
    }

    .curriculum-item:hover .item-duration {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      transform: scale(1.05);
    }

    .item-description {
      padding: 1.5rem 2rem;
      color: var(--dark-text-muted);
      font-size: var(--font-size-base);
      line-height: 1.6;
      border-top: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    /* Video Modal */
    .video-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      padding: 2rem;
      backdrop-filter: blur(10px);
    }

    .video-modal-content {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      max-width: 90vw;
      max-height: 90vh;
      width: 100%;
      max-width: 1000px;
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--dark-border);
    }

    .video-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      background: var(--dark-surface);
      border-bottom: 1px solid var(--dark-border);
    }

    .video-modal-header h3 {
      color: var(--dark-text);
      font-size: var(--font-size-xl);
      font-weight: 700;
      margin: 0;
    }

    .close-video {
      background: none;
      border: none;
      font-size: 2rem;
      color: var(--dark-text-muted);
      cursor: pointer;
      padding: 0.5rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .close-video:hover {
      background: var(--dark-hover);
      color: var(--danger-color);
    }

    .video-modal-body {
      padding: 0;
    }

    .video-modal-body video {
      width: 100%;
      height: auto;
      max-height: 70vh;
      display: block;
    }

    .no-content {
      text-align: center;
      padding: 4rem;
      color: var(--dark-text-muted);
    }

    .no-content i {
      font-size: 4rem;
      margin-bottom: 1.5rem;
      color: var(--dark-border);
    }

    .no-content p {
      font-size: var(--font-size-lg);
      font-weight: 500;
    }

    /* Instructor */
    .instructor-card {
      display: flex;
      gap: 2rem;
      align-items: start;
      padding: 2rem;
      background: var(--dark-surface);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--dark-border);
    }

    .instructor-avatar {
      font-size: 5rem;
      color: var(--primary-color);
      background: rgba(99, 102, 241, 0.1);
      padding: 1.5rem;
      border-radius: var(--border-radius-xl);
      border: 2px solid rgba(99, 102, 241, 0.2);
    }

    .instructor-details h3 {
      color: var(--dark-text);
      margin-bottom: 0.75rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .instructor-title {
      color: var(--primary-color);
      font-weight: 600;
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
    }

    .instructor-bio {
      color: var(--dark-text-light);
      line-height: 1.7;
      font-size: var(--font-size-base);
    }

    /* Course Sidebar */
    .course-sidebar {
      position: sticky;
      top: 2rem;
    }

    .course-card {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--dark-border);
    }

    .preview-image {
      position: relative;
      height: 220px;
      overflow: hidden;
      background: var(--dark-surface);
    }

    .preview-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .preview-play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 70px;
      height: 70px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
      cursor: pointer;
      transition: var(--transition);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .preview-play:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: translate(-50%, -50%) scale(1.1);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-color);
    }

    .price-info {
      padding: 2rem;
      text-align: center;
      border-bottom: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .current-price {
      font-size: var(--font-size-4xl);
      font-weight: 800;
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .price-note {
      display: block;
      font-size: var(--font-size-sm);
      color: var(--dark-text-muted);
      font-weight: 500;
    }

    .course-actions {
      padding: 2rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      background: var(--dark-card);
    }

    .course-includes {
      padding: 2rem;
      border-top: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .course-includes h4 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
      font-weight: 700;
    }

    .course-includes ul {
      list-style: none;
      padding: 0;
    }

    .course-includes li {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      color: var(--dark-text-light);
      font-size: var(--font-size-base);
      font-weight: 500;
    }

    .course-includes li i {
      color: var(--success-color);
      font-size: var(--font-size-base);
      background: rgba(16, 185, 129, 0.1);
      padding: 0.5rem;
      border-radius: var(--border-radius);
    }
/* Related Courses Section */
.related-courses {
  padding: 40px 20px;
  background-color: var(--dark-surface);
}

.related-courses h2 {
  text-align: center;
  color: var(--dark-text);
  margin-bottom: 3rem;
  font-size: var(--font-size-3xl);
  font-weight: 800;
}

/* Grid */
.related-courses .courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

/* Card */
.related-courses .course-card {
  background-color: var(--light-surface);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--dark-border);
}

.related-courses .course-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

/* Image */
.related-courses .course-card .course-image {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.related-courses .course-card .course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.related-courses .course-card:hover .course-image img {
  transform: scale(1.05);
}

/* Price badge */
.related-courses .course-price {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: #fff;
  padding: 5px 10px;
  font-weight: 600;
  border-radius: 6px;
  font-size: 0.875rem;
}

/* Content */
.related-courses .course-content {
  padding: 1.5rem;
}

.related-courses .course-content h3 {
  margin-bottom: 0.5rem;
  font-size: var(--font-size-lg);
  color: var(--dark-text);
  font-weight: 700;
  line-height: 1.4;
}

.related-courses .course-content p {
  color: var(--dark-text-muted);
  font-weight: 500;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Rating */
.related-courses .course-content .rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--dark-text-light);
}

.related-courses .course-content .rating i {
  color: var(--warning-color);
}

/* No content state */
.related-courses .no-content {
  text-align: center;
  color: var(--dark-text-muted);
  font-size: 1rem;
}
.related-courses .no-content i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}


    /* Loading Spinner */
    .loading-spinner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(15, 15, 35, 0.9);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      backdrop-filter: blur(5px);
    }

    .loading-spinner .spinner {
      width: 60px;
      height: 60px;
      border: 4px solid var(--dark-border);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Footer */
    .footer {
      background: var(--dark-surface);
      border-top: 1px solid var(--dark-border);
      padding: 3rem 0 1rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h4 {
      color: var(--dark-text);
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .footer-section p,
    .footer-section li {
      color: var(--dark-text-muted);
      line-height: 1.6;
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: var(--dark-text-muted);
      text-decoration: none;
      transition: var(--transition);
    }

    .footer-section ul li a:hover {
      color: var(--primary-color);
    }

    .footer-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .footer-brand i {
      font-size: 1.5rem;
    }

    .contact-info {
      margin-bottom: 1rem;
    }

    .contact-info p {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .contact-info i {
      color: var(--primary-color);
    }

    .social-links {
      display: flex;
      gap: 1rem;
    }

    .social-links a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: var(--dark-card);
      border-radius: var(--border-radius);
      color: var(--dark-text-muted);
      transition: var(--transition);
    }

    .social-links a:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }

    .footer-bottom {
      text-align: center;
      padding-top: 2rem;
      border-top: 1px solid var(--dark-border);
      color: var(--dark-text-muted);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .course-layout {
        grid-template-columns: 1fr 350px;
        gap: 3rem;
      }
    }

    @media (max-width: 1024px) {
      .course-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .course-sidebar {
        position: static;
        order: -1;
      }

      .course-title {
        font-size: var(--font-size-3xl);
      }

      .course-meta {
        gap: 2rem;
      }

      .curriculum-stats {
        gap: 2rem;
      }
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }

      .mobile-toggle {
        display: flex;
      }

      .user-menu {
        flex-direction: column;
        gap: 0.5rem;
      }

      .user-name {
        font-size: var(--font-size-sm);
      }

      .course-header {
        padding: 2rem 0 3rem;
      }

      .course-title {
        font-size: var(--font-size-2xl);
      }

      .course-subtitle {
        font-size: var(--font-size-lg);
      }

      .course-meta {
        gap: 1.5rem;
      }

      .course-tabs {
        flex-wrap: wrap;
      }

      .tab-btn {
        flex: 1 1 50%;
        font-size: var(--font-size-sm);
      }

      .tab-content {
        padding: 2rem;
      }

      .curriculum-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
      }

      .curriculum-stats {
        justify-content: center;
        gap: 2rem;
      }

      .instructor-card {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .curriculum-item-header {
        padding: 1.25rem 1.5rem;
      }

      .item-description {
        padding: 1.25rem 1.5rem;
      }

      .video-thumbnail {
        width: 60px;
        height: 45px;
      }

      .item-info {
        gap: 1rem;
      }

      .video-modal {
        padding: 1rem;
      }

      .video-modal-header {
        padding: 1rem 1.5rem;
      }

      .video-modal-header h3 {
        font-size: var(--font-size-lg);
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 0 0.75rem;
      }

      .course-header {
        padding: 1.5rem 0 2rem;
      }

      .course-title {
        font-size: var(--font-size-xl);
      }

      .course-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: 2rem;
      }

      .course-meta {
        flex-direction: column;
        gap: 1rem;
      }

      .tab-btn {
        flex: 1 1 100%;
        font-size: var(--font-size-sm);
        padding: 1rem;
      }

      .tab-content {
        padding: 1.5rem;
      }

      .course-description h3 {
        font-size: var(--font-size-xl);
      }

      .course-features h3 {
        font-size: var(--font-size-xl);
      }

      .curriculum-header h3 {
        font-size: var(--font-size-xl);
      }

      .feature-item {
        padding: 1.25rem;
      }

      .instructor-card {
        padding: 1.5rem;
      }

      .instructor-avatar {
        font-size: 4rem;
        padding: 1.25rem;
      }

      .course-actions {
        padding: 1.5rem;
      }

      .course-includes {
        padding: 1.5rem;
      }

      .price-info {
        padding: 1.5rem;
      }

      .current-price {
        font-size: var(--font-size-3xl);
      }

      .video-thumbnail {
        width: 50px;
        height: 38px;
      }

      .item-info {
        gap: 0.75rem;
      }

      .video-details {
        gap: 0.25rem;
      }

      .item-title {
        font-size: var(--font-size-base);
      }

      .video-number {
        font-size: var(--font-size-xs);
      }

      .item-duration {
        padding: 0.25rem 0.75rem;
        font-size: var(--font-size-sm);
      }

      .video-modal {
        padding: 0.5rem;
      }

      .video-modal-content {
        max-width: 95vw;
        max-height: 95vh;
      }

      .video-modal-header {
        padding: 1rem;
      }

      .video-modal-header h3 {
        font-size: var(--font-size-base);
      }

      .close-video {
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
      }
    }

    /* Accessibility */
    .course-card:focus,
    .tab-btn:focus,
    .btn:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
    }

    .curriculum-item-header:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: -2px;
    }

    /* Smooth Scrolling */
    html {
      scroll-behavior: smooth;
    }

    /* Selection Styles */
    ::selection {
      background: var(--primary-color);
      color: white;
    }

    ::-moz-selection {
      background: var(--primary-color);
      color: white;
    }