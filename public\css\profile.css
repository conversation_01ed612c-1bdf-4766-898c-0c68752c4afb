/* Profile Page Styles - Arabic RTL */

:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;

  /* Grays */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Layout */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Amiri', Arial, Tahoma, sans-serif;
  background: var(--gray-900);
  min-height: 100vh;
  color: var(--gray-100);
  line-height: 1.6;
  direction: rtl;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, rgba(16, 185, 129, 0.1) 100%);
  pointer-events: none;
  z-index: -1;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-100);
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: var(--primary-light);
  text-decoration: none;
  transform: translateY(-1px);
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.nav-profile-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--gray-200);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-details span {
  font-weight: 600;
  color: var(--gray-100);
}

.user-details small {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: var(--gray-300);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}

.nav-link:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--gray-100);
  text-decoration: none;
  transform: translateY(-1px);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--danger-color);
}

.logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  transform: translateY(-1px);
}

/* Profile Container */
.profile-container {
  margin-top: 80px;
  padding: 4rem 0;
  min-height: calc(100vh - 80px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rem;
  position: relative;
}

.header-content {
  text-align: center;
  flex: 1;
}

.header-decoration {
  display: flex;
  gap: 1rem;
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
}

.decoration-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--info-color));
  animation: pulse 2s infinite;
}

.decoration-circle:nth-child(2) {
  animation-delay: 0.5s;
}

.decoration-circle:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.profile-title {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  color: var(--gray-100);
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, var(--primary-light), var(--info-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-300);
  font-weight: 400;
}

/* Profile Card */
.profile-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  padding: 2.5rem;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.4);
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--info-color));
}

/* Profile Avatar Section */
.profile-avatar-section {
  margin-bottom: 2rem;
}

.profile-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.profile-status-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: var(--success-color);
  border: 3px solid rgba(30, 41, 59, 0.8);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-light);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.profile-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(37, 99, 235, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.profile-image-overlay i {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.profile-image-overlay span {
  color: white;
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.profile-image-container:hover .profile-image-overlay {
  opacity: 1;
}

/* Profile Info Section */
.profile-info-section {
  margin-bottom: 2rem;
}

.profile-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-100);
  margin-bottom: 0.75rem;
}

.profile-email {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: var(--gray-300);
  font-size: var(--font-size-base);
  margin-bottom: 1rem;
}

.profile-role {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;
}

.profile-role:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

/* Profile Stats Section */
.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: var(--border-radius);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  flex: 1;
}

.stat-item:hover {
  background: rgba(15, 23, 42, 0.7);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
}

.stat-content {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-light);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

/* Profile Settings */
.profile-settings {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-settings:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.4);
}

.profile-tabs .nav-tabs {
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  background: rgba(15, 23, 42, 0.5);
  margin: 0;
  padding: 0 1rem;
  position: relative;
  overflow: hidden;
}

.profile-tabs .nav-tabs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.05) 25%,
    rgba(16, 185, 129, 0.05) 75%,
    transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.profile-tabs .nav-item {
  margin-bottom: 0;
}

.profile-tabs .nav-link {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  color: var(--gray-400);
  font-weight: 500;
  border: none;
  border-bottom: 4px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: none;
  padding: 2rem 2.5rem;
  border-radius: 0;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.profile-tabs .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-tabs .nav-link:hover {
  color: var(--primary-light);
  text-decoration: none;
  transform: translateY(-3px);
}

.profile-tabs .nav-link:hover::before {
  opacity: 1;
}

.profile-tabs .nav-link.active {
  color: var(--primary-light);
  border-bottom-color: var(--primary-light);
  background: rgba(30, 41, 59, 0.9);
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.profile-tabs .nav-link.active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
}

.tab-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.5rem;
  position: relative;
  overflow: hidden;
}

.tab-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-light), var(--info-color));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.tab-icon i {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.profile-tabs .nav-link:hover .tab-icon,
.profile-tabs .nav-link.active .tab-icon {
  transform: scale(1.15) rotate(5deg);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.profile-tabs .nav-link:hover .tab-icon::before,
.profile-tabs .nav-link.active .tab-icon::before {
  opacity: 1;
}

.profile-tabs .nav-link:hover .tab-icon i,
.profile-tabs .nav-link.active .tab-icon i {
  color: white;
  transform: scale(1.1);
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tab-title {
  font-weight: 700;
  font-size: var(--font-size-lg);
  line-height: 1.2;
}

.tab-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  font-weight: 400;
}

.tab-content {
  padding: 0;
}

.tab-pane {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-pane.show.active {
  opacity: 1;
  transform: translateY(0);
}

/* Tab Ripple Effect */
.tab-ripple {
  position: absolute;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

/* Prevent tab switching during animation */
.tab-switching .profile-tabs .nav-link {
  pointer-events: none;
}

/* Enhanced tab hover effects */
.profile-tabs .nav-link:hover .tab-icon {
  animation: tabIconPulse 0.6s ease-in-out;
}

@keyframes tabIconPulse {
  0%, 100% { transform: scale(1.15) rotate(5deg); }
  50% { transform: scale(1.25) rotate(-5deg); }
}

/* Profile Section Cards */
.profile-section-card {
  padding: 3rem;
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.profile-section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.02) 0%,
    rgba(16, 185, 129, 0.02) 50%,
    rgba(59, 130, 246, 0.02) 100%);
  pointer-events: none;
}

.profile-section-card h2 {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--gray-100);
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, var(--primary-light), var(--info-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--gray-200);
  margin-bottom: 0.5rem;
  font-size: var(--font-size-sm);
}

.form-control {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  color: var(--gray-100);
  background: rgba(15, 23, 42, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
  position: relative;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 4px 20px rgba(59, 130, 246, 0.1);
  background: rgba(15, 23, 42, 0.95);
  transform: translateY(-2px);
}

.form-control:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(15, 23, 42, 0.9);
}

.form-control.is-valid {
  border-color: var(--success-color);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
}

.form-text {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
  margin-top: 0.25rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Image Upload Area */
.image-upload-area {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: var(--border-radius-lg);
  background: rgba(15, 23, 42, 0.5);
  transition: all 0.3s ease;
}

.image-upload-area:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(15, 23, 42, 0.7);
}

.upload-preview {
  flex-shrink: 0;
}

.upload-preview img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.upload-preview img:hover {
  border-color: rgba(59, 130, 246, 0.6);
  transform: scale(1.05);
}

.upload-controls {
  flex: 1;
}

.upload-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.upload-label:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.form-control-file {
  display: none;
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: white;
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer-content {
  text-align: center;
}

.footer-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: var(--font-size-lg);
  font-weight: 700;
  margin-bottom: 1rem;
}

.footer p {
  color: var(--gray-400);
  margin: 0;
}

/* Loading Spinner */
.loading-spinner {
  display: none;
  position: fixed;
  z-index: 3000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOutUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.animate-fade-in {
  animation: slideInDown 0.3s ease-out;
}

/* Alert Styles */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.alert-success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert .close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  margin-right: auto;
}

.alert .close:hover {
  opacity: 1;
}

/* Loading States */
.profile-card.loading,
.profile-settings.loading {
  opacity: 0.7;
  pointer-events: none;
}

.profile-card.loading::after,
.profile-settings.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-top: 3px solid var(--primary-light);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Form Group States */
.form-group.focused label {
  color: var(--primary-light);
  transform: translateY(-2px);
}

.form-group.has-success .form-control {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group.has-error .form-control {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Mobile Header */
.profile-header.mobile {
  flex-direction: column;
  text-align: center;
}

.profile-header.mobile .header-decoration {
  position: static;
  transform: none;
  margin-top: 1rem;
}

/* Enhanced Transitions */
.profile-card,
.profile-settings,
.stat-item,
.form-control,
.btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.profile-card:hover .profile-image {
  transform: scale(1.05);
}

.stat-item:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Focus States */
.form-control:focus {
  transform: translateY(-1px);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Responsive Design */
@media (max-width: 992px) {
  .profile-container {
    padding: 2rem 0;
  }

  .profile-card {
    padding: 1.5rem;
  }

  .profile-section-card {
    padding: 1.5rem;
  }

  .image-upload-area {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-user {
    display: none;
  }

  .profile-container {
    padding: 1.5rem 0;
  }

  .profile-title {
    font-size: var(--font-size-2xl);
  }

  .profile-image {
    width: 120px !important;
    height: 120px !important;
  }

  .profile-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    justify-content: center;
  }

  .profile-tabs .nav-link {
    padding: 1.5rem 1rem;
    font-size: var(--font-size-sm);
    flex-direction: column;
    gap: 1rem;
  }

  .tab-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .tab-content {
    text-align: center;
  }

  .tab-title {
    font-size: var(--font-size-base);
  }

  .tab-subtitle {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 0.5rem;
  }

  .profile-card {
    padding: 1rem;
  }

  .profile-section-card {
    padding: 1rem;
  }

  .profile-image {
    width: 100px !important;
    height: 100px !important;
  }

  .upload-preview img {
    width: 80px;
    height: 80px;
  }
}
