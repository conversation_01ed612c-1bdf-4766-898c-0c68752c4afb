<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تفاصيل الدورة - منصة دوراتي</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Enhanced Dark Theme Variables */
    :root {
      --primary-color: #6366f1;
      --primary-dark: #4f46e5;
      --primary-light: #8b5cf6;
      --secondary-color: #10b981;
      --accent-color: #f59e0b;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --info-color: #3b82f6;

      --dark-bg: #0f0f23;
      --dark-surface: #1a1a2e;
      --dark-card: #16213e;
      --dark-hover: #0e3460;
      --dark-border: #2d3748;
      --dark-text: #e2e8f0;
      --dark-text-muted: #94a3b8;
      --dark-text-light: #cbd5e0;

      --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --font-size-xs: 0.75rem;
      --font-size-sm: 0.875rem;
      --font-size-base: 1rem;
      --font-size-lg: 1.125rem;
      --font-size-xl: 1.25rem;
      --font-size-2xl: 1.5rem;
      --font-size-3xl: 1.875rem;
      --font-size-4xl: 2.25rem;

      --border-radius: 0.75rem;
      --border-radius-lg: 1rem;
      --border-radius-xl: 1.25rem;

      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
      --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.3);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.4);

      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--dark-text);
      direction: rtl;
      min-height: 100vh;
      padding-top: 80px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    /* Navigation */
    .navbar {
      background: rgba(26, 26, 46, 0.95);
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-lg);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 1rem 0;
      border-bottom: 1px solid var(--dark-border);
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--primary-color);
      text-decoration: none;
      transition: var(--transition);
    }

    .navbar-brand:hover {
      color: var(--primary-light);
      transform: scale(1.05);
    }

    .navbar-brand i {
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-link {
      color: var(--dark-text-light);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
      position: relative;
    }

    .nav-link:hover,
    .nav-link.active {
      color: var(--primary-color);
      background: rgba(99, 102, 241, 0.1);
    }

    .nav-cta {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-menu {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-name {
      color: var(--dark-text);
      font-weight: 600;
      font-size: var(--font-size-base);
    }

    .mobile-toggle {
      display: none;
      flex-direction: column;
      cursor: pointer;
      gap: 0.25rem;
    }

    .mobile-toggle span {
      width: 25px;
      height: 3px;
      background: var(--dark-text);
      border-radius: 2px;
      transition: var(--transition);
    }

    /* Buttons */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      font-size: var(--font-size-base);
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: var(--transition);
      white-space: nowrap;
      font-family: inherit;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      filter: brightness(1.1);
    }

    .btn-secondary {
      background: var(--dark-card);
      color: var(--dark-text);
      border: 1px solid var(--dark-border);
    }

    .btn-secondary:hover {
      background: var(--dark-hover);
      border-color: var(--primary-color);
    }

    .btn-outline {
      background: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-outline:hover {
      background: var(--primary-color);
      color: white;
    }

    .btn-large {
      padding: 1.25rem 2rem;
      font-size: var(--font-size-lg);
    }

    /* Breadcrumb */
    .breadcrumb {
      padding: 1.5rem 0;
      color: var(--dark-text-muted);
      font-size: var(--font-size-base);
      background: var(--dark-surface);
      border-bottom: 1px solid var(--dark-border);
    }

    .breadcrumb a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
    }

    .breadcrumb a:hover {
      color: var(--primary-light);
      text-decoration: underline;
    }

    .breadcrumb span {
      margin: 0 0.75rem;
      color: var(--dark-text-muted);
    }

    /* Course Header */
    .course-header {
      background:#0f0f23 ;
      color: white;
      padding: 3rem 0 5rem;
      position: relative;
      overflow: hidden;
    }

    .course-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #0f0f23 ;
      opacity: 0.5;
    }

    .course-header-info {
      position: relative;
      z-index: 1;
      max-width: 800px;
    }

    .course-category {
      background: rgba(255, 255, 255, 0.568);
      display: inline-block;
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-base);
      font-weight: 600;
      margin-bottom: 1.5rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .course-title {
      font-size: var(--font-size-4xl);
      font-weight: 800;
      margin-bottom: 1.5rem;
      line-height: 1.2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .course-subtitle {
      font-size: var(--font-size-xl);
      opacity: 0.95;
      margin-bottom: 2.5rem;
      line-height: 1.6;
      font-weight: 400;
    }

    .course-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 2.5rem;
      margin-bottom: 2rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: var(--font-size-base);
      font-weight: 500;
      background: rgba(255, 255, 255, 0.1);
      padding: 0.75rem 1.25rem;
      border-radius: var(--border-radius);
      backdrop-filter: blur(10px);
    }

    .meta-item i {
      color: rgba(255, 255, 255, 0.9);
      font-size: var(--font-size-lg);
    }

    .course-instructor-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: var(--font-size-lg);
      font-weight: 600;
      background: rgba(255, 255, 255, 0.15);
      padding: 1rem 1.5rem;
      border-radius: var(--border-radius);
      backdrop-filter: blur(10px);
      display: inline-flex;
    }

    .course-instructor-info i {
      font-size: var(--font-size-xl);
    }

    /* Course Content */
    .course-content {
      padding: 4rem 0;
    }

    .course-layout {
      display: grid;
      grid-template-columns: 1fr 380px;
      gap: 4rem;
      align-items: start;
    }

    /* Course Main */
    .course-main {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--dark-border);
    }

    /* Course Tabs */
    .course-tabs {
      display: flex;
      border-bottom: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .tab-btn {
      flex: 1;
      padding: 1.25rem 1.5rem;
      border: none;
      background: transparent;
      color: var(--dark-text-muted);
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      font-size: var(--font-size-base);
      font-family: inherit;
    }

    .tab-btn:hover {
      background: var(--dark-hover);
      color: var(--dark-text);
    }

    .tab-btn.active {
      background: var(--dark-card);
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }

    /* Tab Content */
    .tab-content {
      padding: 2.5rem;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
      animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Course Description */
    .course-description h3 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .course-description p {
      color: var(--dark-text-light);
      line-height: 1.8;
      margin-bottom: 2rem;
      font-size: var(--font-size-lg);
    }

    .course-tags {
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid var(--dark-border);
    }

    .course-tags h4 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
      font-weight: 600;
    }

    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
    }

    .tag {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-sm);
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      transition: var(--transition);
    }

    .tag:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    /* Course Features */
    .course-features {
      margin-top: 3rem;
    }

    .course-features h3 {
      color: var(--dark-text);
      margin-bottom: 2rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: var(--dark-surface);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--dark-border);
      transition: var(--transition);
    }

    .feature-item:hover {
      background: var(--dark-hover);
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .feature-item i {
      color: var(--success-color);
      font-size: var(--font-size-xl);
      background: rgba(16, 185, 129, 0.1);
      padding: 0.75rem;
      border-radius: var(--border-radius);
    }

    .feature-item span {
      font-weight: 500;
      color: var(--dark-text-light);
      font-size: var(--font-size-base);
    }

    /* Curriculum */
    .curriculum-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2.5rem;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .curriculum-header h3 {
      color: var(--dark-text);
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .curriculum-stats {
      display: flex;
      gap: 3rem;
    }

    .stat {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: var(--font-size-2xl);
      font-weight: 800;
      color: var(--primary-color);
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: var(--font-size-base);
      color: var(--dark-text-muted);
      font-weight: 500;
    }

    .curriculum-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .curriculum-item {
      border: 1px solid var(--dark-border);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      transition: var(--transition);
      cursor: pointer;
      background: var(--dark-surface);
      box-shadow: var(--shadow-sm);
    }

    .curriculum-item:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }

    .curriculum-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      background: var(--dark-card);
    }

    .item-info {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      flex: 1;
    }

    .video-thumbnail {
      width: 100px;
      height: 75px;
      border-radius: var(--border-radius-lg);
      overflow: hidden;
      background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      flex-shrink: 0;
      border: 2px solid var(--dark-border);
      transition: var(--transition);
    }

    .curriculum-item:hover .video-thumbnail {
      border-color: var(--primary-color);
      transform: scale(1.05);
    }

    .video-thumb-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: var(--transition);
    }

    .video-thumb-placeholder {
      color: var(--primary-color);
      font-size: var(--font-size-2xl);
      background: rgba(99, 102, 241, 0.1);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .video-details {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      flex: 1;
    }

    .item-title {
      font-weight: 700;
      color: var(--dark-text);
      font-size: var(--font-size-xl);
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .video-number {
      font-size: var(--font-size-sm);
      color: var(--primary-color);
      font-weight: 600;
      background: rgba(99, 102, 241, 0.1);
      padding: 0.25rem 0.75rem;
      border-radius: var(--border-radius);
      display: inline-block;
      width: fit-content;
      border: 1px solid rgba(99, 102, 241, 0.2);
    }

    .item-duration {
      color: var(--dark-text);
      font-size: var(--font-size-base);
      font-weight: 700;
      background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
      padding: 0.75rem 1.25rem;
      border-radius: var(--border-radius-lg);
      border: 2px solid var(--dark-border);
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: var(--transition);
      box-shadow: var(--shadow-sm);
    }

    .curriculum-item:hover .item-duration {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      transform: scale(1.05);
    }

    .item-description {
      padding: 1.5rem 2rem;
      color: var(--dark-text-muted);
      font-size: var(--font-size-base);
      line-height: 1.6;
      border-top: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    /* Video Modal */
    .video-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      padding: 2rem;
      backdrop-filter: blur(10px);
    }

    .video-modal-content {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      max-width: 90vw;
      max-height: 90vh;
      width: 100%;
      max-width: 1000px;
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--dark-border);
    }

    .video-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      background: var(--dark-surface);
      border-bottom: 1px solid var(--dark-border);
    }

    .video-modal-header h3 {
      color: var(--dark-text);
      font-size: var(--font-size-xl);
      font-weight: 700;
      margin: 0;
    }

    .close-video {
      background: none;
      border: none;
      font-size: 2rem;
      color: var(--dark-text-muted);
      cursor: pointer;
      padding: 0.5rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .close-video:hover {
      background: var(--dark-hover);
      color: var(--danger-color);
    }

    .video-modal-body {
      padding: 0;
    }

    .video-modal-body video {
      width: 100%;
      height: auto;
      max-height: 70vh;
      display: block;
    }

    .no-content {
      text-align: center;
      padding: 4rem;
      color: var(--dark-text-muted);
    }

    .no-content i {
      font-size: 4rem;
      margin-bottom: 1.5rem;
      color: var(--dark-border);
    }

    .no-content p {
      font-size: var(--font-size-lg);
      font-weight: 500;
    }

    /* Instructor */
    .instructor-card {
      display: flex;
      gap: 2rem;
      align-items: start;
      padding: 2rem;
      background: var(--dark-surface);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--dark-border);
    }

    .instructor-avatar {
      font-size: 5rem;
      color: var(--primary-color);
      background: rgba(99, 102, 241, 0.1);
      padding: 1.5rem;
      border-radius: var(--border-radius-xl);
      border: 2px solid rgba(99, 102, 241, 0.2);
    }

    .instructor-details h3 {
      color: var(--dark-text);
      margin-bottom: 0.75rem;
      font-size: var(--font-size-2xl);
      font-weight: 700;
    }

    .instructor-title {
      color: var(--primary-color);
      font-weight: 600;
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
    }

    .instructor-bio {
      color: var(--dark-text-light);
      line-height: 1.7;
      font-size: var(--font-size-base);
    }

    /* Course Sidebar */
    .course-sidebar {
      position: sticky;
      top: 2rem;
    }

    .course-card {
      background: var(--dark-card);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--dark-border);
    }

    .preview-image {
      position: relative;
      height: 220px;
      overflow: hidden;
      background: var(--dark-surface);
    }

    .preview-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .preview-play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 70px;
      height: 70px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
      cursor: pointer;
      transition: var(--transition);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .preview-play:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: translate(-50%, -50%) scale(1.1);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-color);
    }

    .price-info {
      padding: 2rem;
      text-align: center;
      border-bottom: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .current-price {
      font-size: var(--font-size-4xl);
      font-weight: 800;
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .price-note {
      display: block;
      font-size: var(--font-size-sm);
      color: var(--dark-text-muted);
      font-weight: 500;
    }

    .course-actions {
      padding: 2rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      background: var(--dark-card);
    }

    .course-includes {
      padding: 2rem;
      border-top: 1px solid var(--dark-border);
      background: var(--dark-surface);
    }

    .course-includes h4 {
      color: var(--dark-text);
      margin-bottom: 1.5rem;
      font-size: var(--font-size-lg);
      font-weight: 700;
    }

    .course-includes ul {
      list-style: none;
      padding: 0;
    }

    .course-includes li {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      color: var(--dark-text-light);
      font-size: var(--font-size-base);
      font-weight: 500;
    }

    .course-includes li i {
      color: var(--success-color);
      font-size: var(--font-size-base);
      background: rgba(16, 185, 129, 0.1);
      padding: 0.5rem;
      border-radius: var(--border-radius);
    }

    /* Related Courses */
    .related-courses {
      padding: 5rem 0;
      background: var(--dark-surface);
    }

    .related-courses h2 {
      text-align: center;
      color: var(--dark-text);
      margin-bottom: 4rem;
      font-size: var(--font-size-3xl);
      font-weight: 800;
    }

    .related-courses .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 2rem;
    }

    .related-courses .course-card {
      cursor: pointer;
      transition: var(--transition);
      border: 1px solid var(--dark-border);
    }

    .related-courses .course-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-xl);
      border-color: var(--primary-color);
    }

    /* Loading Spinner */
    .loading-spinner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(15, 15, 35, 0.9);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      backdrop-filter: blur(5px);
    }

    .loading-spinner .spinner {
      width: 60px;
      height: 60px;
      border: 4px solid var(--dark-border);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Footer */
    .footer {
      background: var(--dark-surface);
      border-top: 1px solid var(--dark-border);
      padding: 3rem 0 1rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h4 {
      color: var(--dark-text);
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .footer-section p,
    .footer-section li {
      color: var(--dark-text-muted);
      line-height: 1.6;
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: var(--dark-text-muted);
      text-decoration: none;
      transition: var(--transition);
    }

    .footer-section ul li a:hover {
      color: var(--primary-color);
    }

    .footer-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .footer-brand i {
      font-size: 1.5rem;
    }

    .contact-info {
      margin-bottom: 1rem;
    }

    .contact-info p {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .contact-info i {
      color: var(--primary-color);
    }

    .social-links {
      display: flex;
      gap: 1rem;
    }

    .social-links a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: var(--dark-card);
      border-radius: var(--border-radius);
      color: var(--dark-text-muted);
      transition: var(--transition);
    }

    .social-links a:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }

    .footer-bottom {
      text-align: center;
      padding-top: 2rem;
      border-top: 1px solid var(--dark-border);
      color: var(--dark-text-muted);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .course-layout {
        grid-template-columns: 1fr 350px;
        gap: 3rem;
      }
    }

    @media (max-width: 1024px) {
      .course-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .course-sidebar {
        position: static;
        order: -1;
      }

      .course-title {
        font-size: var(--font-size-3xl);
      }

      .course-meta {
        gap: 2rem;
      }

      .curriculum-stats {
        gap: 2rem;
      }
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }

      .mobile-toggle {
        display: flex;
      }

      .user-menu {
        flex-direction: column;
        gap: 0.5rem;
      }

      .user-name {
        font-size: var(--font-size-sm);
      }

      .course-header {
        padding: 2rem 0 3rem;
      }

      .course-title {
        font-size: var(--font-size-2xl);
      }

      .course-subtitle {
        font-size: var(--font-size-lg);
      }

      .course-meta {
        gap: 1.5rem;
      }

      .course-tabs {
        flex-wrap: wrap;
      }

      .tab-btn {
        flex: 1 1 50%;
        font-size: var(--font-size-sm);
      }

      .tab-content {
        padding: 2rem;
      }

      .curriculum-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
      }

      .curriculum-stats {
        justify-content: center;
        gap: 2rem;
      }

      .instructor-card {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .curriculum-item-header {
        padding: 1.25rem 1.5rem;
      }

      .item-description {
        padding: 1.25rem 1.5rem;
      }

      .video-thumbnail {
        width: 60px;
        height: 45px;
      }

      .item-info {
        gap: 1rem;
      }

      .video-modal {
        padding: 1rem;
      }

      .video-modal-header {
        padding: 1rem 1.5rem;
      }

      .video-modal-header h3 {
        font-size: var(--font-size-lg);
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 0 0.75rem;
      }

      .course-header {
        padding: 1.5rem 0 2rem;
      }

      .course-title {
        font-size: var(--font-size-xl);
      }

      .course-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: 2rem;
      }

      .course-meta {
        flex-direction: column;
        gap: 1rem;
      }

      .tab-btn {
        flex: 1 1 100%;
        font-size: var(--font-size-sm);
        padding: 1rem;
      }

      .tab-content {
        padding: 1.5rem;
      }

      .course-description h3 {
        font-size: var(--font-size-xl);
      }

      .course-features h3 {
        font-size: var(--font-size-xl);
      }

      .curriculum-header h3 {
        font-size: var(--font-size-xl);
      }

      .feature-item {
        padding: 1.25rem;
      }

      .instructor-card {
        padding: 1.5rem;
      }

      .instructor-avatar {
        font-size: 4rem;
        padding: 1.25rem;
      }

      .course-actions {
        padding: 1.5rem;
      }

      .course-includes {
        padding: 1.5rem;
      }

      .price-info {
        padding: 1.5rem;
      }

      .current-price {
        font-size: var(--font-size-3xl);
      }

      .video-thumbnail {
        width: 50px;
        height: 38px;
      }

      .item-info {
        gap: 0.75rem;
      }

      .video-details {
        gap: 0.25rem;
      }

      .item-title {
        font-size: var(--font-size-base);
      }

      .video-number {
        font-size: var(--font-size-xs);
      }

      .item-duration {
        padding: 0.25rem 0.75rem;
        font-size: var(--font-size-sm);
      }

      .video-modal {
        padding: 0.5rem;
      }

      .video-modal-content {
        max-width: 95vw;
        max-height: 95vh;
      }

      .video-modal-header {
        padding: 1rem;
      }

      .video-modal-header h3 {
        font-size: var(--font-size-base);
      }

      .close-video {
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
      }
    }

    /* Accessibility */
    .course-card:focus,
    .tab-btn:focus,
    .btn:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
    }

    .curriculum-item-header:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: -2px;
    }

    /* Smooth Scrolling */
    html {
      scroll-behavior: smooth;
    }

    /* Selection Styles */
    ::selection {
      background: var(--primary-color);
      color: white;
    }

    ::-moz-selection {
      background: var(--primary-color);
      color: white;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar" id="navbar">
    <div class="nav-container">
      <a href="/" class="navbar-brand">
        <i class="fas fa-graduation-cap"></i>
        منصة دوراتي
      </a>

      <ul class="nav-links">
        <li><a href="/" class="nav-link">الرئيسية</a></li>
        <li><a href="/courses.html" class="nav-link">الدورات</a></li>
        <li><a href="#about" class="nav-link">من نحن</a></li>
        <li><a href="#contact" class="nav-link">تواصل معنا</a></li>
      </ul>

      <div class="nav-cta" id="navAuth">
        <!-- Auth buttons will be loaded here -->
        <a href="/login.html" class="btn btn-secondary">تسجيل الدخول</a>
        <a href="/register.html" class="btn btn-primary">ابدأ الآن</a>
      </div>

      <div class="mobile-toggle" id="mobile-toggle">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Course Header -->
  <section class="course-header">
    <div class="container">
      <div class="breadcrumb">
        <a href="/">الرئيسية</a>
        <span>/</span>
        <a href="/courses.html">الدورات</a>
        <span>/</span>
        <span id="courseBreadcrumb">تفاصيل الدورة</span>
      </div>
      
      <div class="course-header-content" id="courseHeaderContent">
        <!-- Course header will be loaded here -->
      </div>
    </div>
  </section>

  <!-- Course Content -->
  <section class="course-content">
    <div class="container">
      <div class="course-layout">
        <!-- Course Info -->
        <div class="course-main">
          <div class="course-tabs">
            <button class="tab-btn active" data-tab="overview">نظرة عامة</button>
            <button class="tab-btn" data-tab="curriculum">المنهج</button>
            <button class="tab-btn" data-tab="instructor">المدرب</button>
            <button class="tab-btn" data-tab="reviews">التقييمات</button>
          </div>
          
          <div class="tab-content">
            <!-- Overview Tab -->
            <div id="overview" class="tab-pane active">
              <div class="course-description" id="courseDescription">
                <!-- Course description will be loaded here -->
              </div>
              
              <div class="course-features">
                <h3>ما ستتعلمه في هذه الدورة</h3>
                <div class="features-grid" id="courseFeatures">
                  <!-- Course features will be loaded here -->
                </div>
              </div>
            </div>
            
            <!-- Curriculum Tab -->
            <div id="curriculum" class="tab-pane">
              <div class="curriculum-content">
                <div class="curriculum-header">
                  <h3>محتوى الدورة</h3>
                  <div class="curriculum-stats" id="curriculumStats">
                    <!-- Stats will be loaded here -->
                  </div>
                </div>
                
                <div class="curriculum-list" id="curriculumList">
                  <!-- Curriculum will be loaded here -->
                </div>
              </div>
            </div>
            
            <!-- Instructor Tab -->
            <div id="instructor" class="tab-pane">
              <div class="instructor-info" id="instructorInfo">
                <!-- Instructor info will be loaded here -->
              </div>
            </div>
            
            <!-- Reviews Tab -->
            <div id="reviews" class="tab-pane">
              <div class="reviews-content" id="reviewsContent">
                <div class="no-content">
                  <i class="fas fa-star"></i>
                  <p>لا توجد تقييمات لهذه الدورة بعد</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Course Sidebar -->
        <div class="course-sidebar">
          <div class="course-card">
            <div class="course-preview" id="coursePreview">
              <!-- Course preview will be loaded here -->
            </div>
            
            <div class="course-price" id="coursePrice">
              <!-- Course price will be loaded here -->
            </div>
            
            <div class="course-actions" id="courseActions">
              <!-- Course actions will be loaded here -->
            </div>
            
            <div class="course-includes">
              <h4>تتضمن هذه الدورة:</h4>
              <ul id="courseIncludes">
                <!-- Course includes will be loaded here -->
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Related Courses -->
  <section class="related-courses">
    <div class="container">
      <h2>دورات ذات صلة</h2>
      <div class="courses-grid" id="relatedCourses">
        <!-- Related courses will be loaded here -->
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-brand">
            <i class="fas fa-graduation-cap"></i>
            <span>منصة دوراتي</span>
          </div>
          <p>منصة تعليمية متخصصة في تقديم دورات عالية الجودة في مختلف المجالات</p>
        </div>
        
        <div class="footer-section">
          <h4>روابط سريعة</h4>
          <ul>
            <li><a href="/">الرئيسية</a></li>
            <li><a href="/courses.html">الدورات</a></li>
            <li><a href="/about.html">من نحن</a></li>
            <li><a href="/contact.html">تواصل معنا</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>تواصل معنا</h4>
          <div class="contact-info">
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +966 50 123 4567</p>
          </div>
          <div class="social-links">
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-facebook"></i></a>
            <a href="#"><i class="fab fa-instagram"></i></a>
            <a href="#"><i class="fab fa-linkedin"></i></a>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2024 منصة دوراتي. جميع الحقوق محفوظة.</p>
      </div>
    </div>
  </footer>

  <!-- Loading Spinner -->
  <div id="loadingSpinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Scripts -->
  <script>
    // Enhanced Course Detail JavaScript with API Integration
    let currentCourse = null;
    let currentUser = null;

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      initializeAuth();
      updateNavbar();
      initializeTabs();
      loadCourseDetail();
    });

    // Initialize authentication
    function initializeAuth() {
      const token = localStorage.getItem('token');
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      currentUser = user;
    }

    // Update navbar based on authentication status
    function updateNavbar() {
      const navAuth = document.getElementById('navAuth');
      const token = localStorage.getItem('token');
      const user = JSON.parse(localStorage.getItem('user') || '{}');

      if (token && user.name) {
        navAuth.innerHTML = `
          <div class="user-menu">
            <span class="user-name">مرحباً، ${user.name}</span>
            ${user.role === 'admin' ?
              '<a href="/admin-dashboard.html" class="btn btn-secondary">لوحة التحكم</a>' :
              '<a href="/dashboard.html" class="btn btn-secondary">حسابي</a>'
            }
            <button onclick="logout()" class="btn btn-outline">تسجيل الخروج</button>
          </div>
        `;
      } else {
        navAuth.innerHTML = `
          <a href="/login.html" class="btn btn-secondary">تسجيل الدخول</a>
          <a href="/register.html" class="btn btn-primary">ابدأ الآن</a>
        `;
      }
    }

    // Logout function
    function logout() {
      if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
      }
    }

    // Initialize tabs
    function initializeTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabPanes = document.querySelectorAll('.tab-pane');
      
      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');
          
          // Remove active class from all tabs and panes
          tabBtns.forEach(b => b.classList.remove('active'));
          tabPanes.forEach(p => p.classList.remove('active'));
          
          // Add active class to clicked tab and corresponding pane
          this.classList.add('active');
          document.getElementById(targetTab).classList.add('active');
        });
      });
    }

    // Load course detail from API
    async function loadCourseDetail() {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const courseId = urlParams.get('id');
        
        if (!courseId) {
          window.location.href = '/courses.html';
          return;
        }
        
        showLoading();
        
        // Use the provided API endpoint
        const response = await fetch(`http://localhost:4009/api/courses/${courseId}`);
        
        if (response.ok) {
          const responseData = await response.json();
          currentCourse = responseData.data;
          
          displayCourseHeader(currentCourse);
          displayCourseOverview(currentCourse);
          displayCourseCurriculum(currentCourse);
          displayCourseInstructor(currentCourse);
          displayCourseSidebar(currentCourse);

          // Load related courses
          loadRelatedCourses(currentCourse.category, courseId);
          
          // Update page title and breadcrumb
          document.title = `${currentCourse.title} - منصة دوراتي`;
          document.getElementById('courseBreadcrumb').textContent = currentCourse.title;
          
        } else {
          throw new Error('Course not found');
        }
      } catch (error) {
        console.error('Error loading course:', error);
        showError('حدث خطأ في تحميل تفاصيل الدورة');
      } finally {
        hideLoading();
      }
    }

    // Display course header
    function displayCourseHeader(course) {
      const headerContent = document.getElementById('courseHeaderContent');
      
      const categoryNames = {
        development: 'البرمجة والتطوير',
        business: 'الأعمال والإدارة',
        design: 'التصميم والجرافيك',
        marketing: 'التسويق الرقمي',
        languages: 'اللغات',
        other: 'أخرى'
      };
      
      const levelNames = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم'
      };
      
      const rating = course.averageRating > 0 ? course.averageRating.toFixed(1) : 'جديد';
      const enrollmentCount = course.enrollmentCount || 0;
      
      headerContent.innerHTML = `
        <div class="course-header-info">
          <div class="course-category">${categoryNames[course.category] || course.category}</div>
          <h1 class="course-title">${course.title}</h1>
          <p class="course-subtitle">${course.description}</p>
          
          <div class="course-meta">
            <div class="meta-item">
              <i class="fas fa-star"></i>
              <span>${rating}</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-users"></i>
              <span>${enrollmentCount} طالب</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-signal"></i>
              <span>${levelNames[course.level] || course.level}</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-clock"></i>
              <span>آخر تحديث: ${new Date(course.updatedAt).toLocaleDateString('ar-SA')}</span>
            </div>
          </div>
          
          <div class="course-instructor-info">
            <i class="fas fa-user"></i>
            <span>المدرب: ${course.instructor}</span>
          </div>
        </div>
      `;
    }

    // Display course overview
    function displayCourseOverview(course) {
      const descriptionElement = document.getElementById('courseDescription');
      const featuresElement = document.getElementById('courseFeatures');
      
      descriptionElement.innerHTML = `
        <h3>وصف الدورة</h3>
        <p>${course.description}</p>
        
        ${course.tags && course.tags.length > 0 ? `
          <div class="course-tags">
            <h4>الكلمات المفتاحية:</h4>
            <div class="tags-list">
              ${course.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
          </div>
        ` : ''}
      `;
      
      // Sample features - in a real app, these would come from the course data
      const features = [
        'تعلم المفاهيم الأساسية والمتقدمة',
        'مشاريع عملية وتطبيقية',
        'شهادة إتمام معتمدة',
        'دعم فني مستمر',
        'وصول مدى الحياة للمحتوى',
        'تحديثات  للمحتوى'
      ];
      
      featuresElement.innerHTML = features.map(feature => `
        <div class="feature-item">
          <i class="fas fa-check"></i>
          <span>${feature}</span>
        </div>
      `).join('');
    }

    // Display course curriculum using the videos from API response
    function displayCourseCurriculum(course) {
      const statsElement = document.getElementById('curriculumStats');
      const listElement = document.getElementById('curriculumList');

      const videos = course.videos || [];
      const videoCount = videos.length;
      const totalDuration = videos.reduce((total, video) => total + (video.duration || 0), 0);

      const hours = Math.floor(totalDuration / 3600);
      const minutes = Math.floor((totalDuration % 3600) / 60);

      statsElement.innerHTML = `
        <div class="stat">
          <span class="stat-number">${videoCount}</span>
          <span class="stat-label">فيديو</span>
        </div>
        <div class="stat">
          <span class="stat-number">${hours}س ${minutes}د</span>
          <span class="stat-label">المدة الإجمالية</span>
        </div>
      `;

      if (videos.length > 0) {
        listElement.innerHTML = videos
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map((video, videoIndex) => {
            const videoDuration = video.duration ?
              `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}` :
              '--:--';

            return `
              <div class="curriculum-item" onclick="playVideo('${video._id}', '${video.videoUrl}', '${video.title.replace(/'/g, "\\'")}')">
                <div class="curriculum-item-header">
                  <div class="item-info">
                    <div class="video-thumbnail">
                      <div class="video-thumb-placeholder">
                        <i class="fas fa-play-circle"></i>
                      </div>
                    </div>
                    <div class="video-details">
                      <span class="item-title">${video.title}</span>
                      <span class="video-number">الفيديو ${videoIndex + 1}</span>
                    </div>
                  </div>
                  <div class="item-duration">
                    <i class="fas fa-clock"></i>
                    ${videoDuration}
                  </div>
                </div>
                ${video.description ? `
                  <div class="item-description">${video.description}</div>
                ` : ''}
              </div>
            `;
          }).join('');
      } else {
        listElement.innerHTML = `
          <div class="no-content">
            <i class="fas fa-video"></i>
            <p>لم يتم إضافة فيديوهات لهذه الدورة بعد</p>
          </div>
        `;
      }
    }

    // Play video function with enhanced modal
    function playVideo(videoId, videoUrl, videoTitle) {
      // Create video modal
      const modal = document.createElement('div');
      modal.className = 'video-modal';
      modal.innerHTML = `
        <div class="video-modal-content">
          <div class="video-modal-header">
            <h3>${videoTitle}</h3>
            <button class="close-video" onclick="closeVideoModal()">&times;</button>
          </div>
          <div class="video-modal-body">
            <video controls autoplay width="100%" style="max-height: 70vh;">
              <source src="${videoUrl}" type="video/mp4">
              متصفحك لا يدعم تشغيل الفيديو
            </video>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      document.body.style.overflow = 'hidden';
      
      // Close modal on background click
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeVideoModal();
        }
      });
      
      // Close modal on escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          closeVideoModal();
        }
      });
    }

    // Close video modal
    function closeVideoModal() {
      const modal = document.querySelector('.video-modal');
      if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
      }
    }

    // Display course instructor
    function displayCourseInstructor(course) {
      const instructorElement = document.getElementById('instructorInfo');
      
      const categoryInfo = {
        development: 'البرمجة والتطوير',
        business: 'الأعمال والإدارة',
        design: 'التصميم والجرافيك',
        marketing: 'التسويق الرقمي',
        languages: 'اللغات',
        other: 'عام'
      };
      
      instructorElement.innerHTML = `
        <div class="instructor-card">
          <div class="instructor-avatar">
            <i class="fas fa-user-circle"></i>
          </div>
          <div class="instructor-details">
            <h3>${course.instructor}</h3>
            <p class="instructor-title">مدرب معتمد في ${categoryInfo[course.category] || 'التعليم العام'}</p>
            <p class="instructor-bio">
              مدرب متخصص في مجال ${categoryInfo[course.category] || course.category} مع سنوات من الخبرة في التدريس والتطوير.
              يهدف إلى تقديم محتوى تعليمي عالي الجودة يساعد الطلاب على تحقيق أهدافهم المهنية والأكاديمية.
              يتميز بأسلوب تعليمي تفاعلي ومبسط يناسب جميع المستويات.
            </p>
          </div>
        </div>
      `;
    }

    // Display course sidebar
    function displayCourseSidebar(course) {
      const previewElement = document.getElementById('coursePreview');
      const priceElement = document.getElementById('coursePrice');
      const actionsElement = document.getElementById('courseActions');
      const includesElement = document.getElementById('courseIncludes');
      
      // Course preview
      previewElement.innerHTML = `
        <div class="preview-image">
          <img src="${course.thumbnail || '/uploads/no-photo.jpg'}" alt="${course.title}" onerror="this.src='/uploads/no-photo.jpg'">
          <div class="preview-play" onclick="previewCourse()">
            <i class="fas fa-play"></i>
          </div>
        </div>
      `;
      
      // Course price
      const price = course.price > 0 ? `${course.price} ج` : 'مجاني';
      priceElement.innerHTML = `
        <div class="price-info">
          <span class="current-price">${price}</span>
          ${course.price > 0 ? ' ' : '<span class="price-note">دورة مجانية بالكامل</span>'}
        </div>
      `;
      
      // Course actions
      const isLoggedIn = localStorage.getItem('token');
      actionsElement.innerHTML = `
        ${isLoggedIn ? `
        ` : `
          <button class="btn btn-primary btn-large" onclick="redirectToLogin()">
            <i class="fas fa-sign-in-alt"></i>
            سجل دخول للتسجيل
          </button>
        `}
        
        <button class="btn btn-outline btn-large" onclick="addToWishlist('${course._id}')">
          <i class="fas fa-heart"></i>
          إضافة للمفضلة
        </button>
      `;
      
      // Course includes
      const videos = course.videos || [];
      const totalDuration = videos.reduce((total, video) => total + (video.duration || 0), 0);
      const hours = Math.floor(totalDuration / 3600);
      const minutes = Math.floor((totalDuration % 3600) / 60);
      
      const includes = [
        `${videos.length} فيديو تعليمي`,
        `${hours} ساعة و ${minutes} دقيقة من المحتوى`,
        'وصول مدى الحياة',
        'شهادة إتمام معتمدة',
        'دعم فني مستمر',
        'تحديثات للمحتوي'
      ];
      
      includesElement.innerHTML = includes.map(item => `
        <li><i class="fas fa-check"></i> ${item}</li>
      `).join('');
    }

    // Preview course function
    function previewCourse() {
      const videos = currentCourse.videos || [];
      if (videos.length > 0) {
        const firstVideo = videos.sort((a, b) => (a.order || 0) - (b.order || 0))[0];
        playVideo(firstVideo._id, firstVideo.videoUrl, firstVideo.title);
      } else {
        alert('لا توجد فيديوهات معاينة متاحة لهذه الدورة');
      }
    }

    // Load related courses
    async function loadRelatedCourses(category, excludeId) {
      try {
        const response = await fetch(`http://localhost:4009/api/courses?category=${category}&limit=4&isPublished=true`);
        
        if (response.ok) {
          const data = await response.json();
          const courses = data.data || data.courses || [];
          const relatedCourses = courses.filter(course => course._id !== excludeId);
          
          displayRelatedCourses(relatedCourses.slice(0, 3));
        }
      } catch (error) {
        console.error('Error loading related courses:', error);
        // Display empty state
        displayRelatedCourses([]);
      }
    }

    // Display related courses
    function displayRelatedCourses(courses) {
      const relatedElement = document.getElementById('relatedCourses');
      
      if (courses.length === 0) {
        relatedElement.innerHTML = `
          <div class="no-content">
            <i class="fas fa-graduation-cap"></i>
            <p>لا توجد دورات ذات صلة في الوقت الحالي</p>
          </div>
        `;
        return;
      }
      
      const categoryNames = {
        development: 'البرمجة والتطوير',
        business: 'الأعمال والإدارة',
        design: 'التصميم والجرافيك',
        marketing: 'التسويق الرقمي',
        languages: 'اللغات',
        other: 'أخرى'
      };
      
      relatedElement.innerHTML = courses.map(course => {
        const price = course.price > 0 ? `${course.price} ج` : 'مجاني';
        const rating = course.averageRating > 0 ? course.averageRating.toFixed(1) : 'جديد';
        
        return `
          <div class="course-card" onclick="viewCourse('${course._id}')">
            <div class="course-image">
              <img src="${course.thumbnail || '/uploads/no-photo.jpg'}" alt="${course.title}" onerror="this.src='/uploads/no-photo.jpg'">
              <div class="course-price">${price}</div>
            </div>
            
            <div class="course-content" style="padding: 1.5rem;">
              <div style="margin-bottom: 0.5rem;">
                <span style="font-size: 0.875rem; color: var(--primary-color); font-weight: 600;">${categoryNames[course.category] || course.category}</span>
              </div>
              <h3 style="color: var(--dark-text); margin-bottom: 1rem; font-size: var(--font-size-lg); font-weight: 700; line-height: 1.4;">${course.title}</h3>
              <p style="color: var(--dark-text-muted); font-weight: 500; margin-bottom: 1rem;">${course.instructor}</p>
              
              <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-star" style="color: var(--warning-color);"></i>
                <span style="color: var(--dark-text-light); font-weight: 600;">${rating}</span>
              </div>
            </div>
          </div>
        `;
      }).join('');
    }

    // View course
    function viewCourse(courseId) {
      window.location.href = `?id=${courseId}`;
    }

    // Enroll in course
    async function enrollInCourse(courseId) {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          redirectToLogin();
          return;
        }
        
        const response = await fetch('http://localhost:4009/api/enrollments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ course: courseId })
        });
        
        if (response.ok) {
          alert('تم التسجيل في الدورة بنجاح! 🎉');
          // Update enrollment count
          if (currentCourse) {
            currentCourse.enrollmentCount = (currentCourse.enrollmentCount || 0) + 1;
            displayCourseHeader(currentCourse);
          }
          // Redirect to course learning page or dashboard
          window.location.href = '/dashboard.html';
        } else {
          const errorData = await response.json();
          alert(errorData.message || 'حدث خطأ في التسجيل');
        }
      } catch (error) {
        console.error('Error enrolling in course:', error);
        alert('حدث خطأ في التسجيل. يرجى المحاولة مرة أخرى.');
      }
    }

    // Add to wishlist
    function addToWishlist(courseId) {
      // In a real implementation, this would save to the backend
      let wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
      
      if (!wishlist.includes(courseId)) {
        wishlist.push(courseId);
        localStorage.setItem('wishlist', JSON.stringify(wishlist));
        alert('تم إضافة الدورة للمفضلة! ❤️');
      } else {
        alert('هذه الدورة موجودة بالفعل في المفضلة');
      }
    }

    // Redirect to login
    function redirectToLogin() {
      // Store current page URL to redirect back after login
      sessionStorage.setItem('redirectUrl', window.location.href);
      window.location.href = '/login.html';
    }

    // Show loading
    function showLoading() {
      const spinner = document.getElementById('loadingSpinner');
      if (spinner) spinner.style.display = 'flex';
    }

    // Hide loading
    function hideLoading() {
      const spinner = document.getElementById('loadingSpinner');
      if (spinner) spinner.style.display = 'none';
    }

    // Show error
    function showError(message) {
      alert(message);
      // Optionally redirect to courses page
      // window.location.href = '/courses.html';
    }

    // Mobile menu toggle
    document.getElementById('mobile-toggle')?.addEventListener('click', function() {
      const navLinks = document.querySelector('.nav-links');
      navLinks.classList.toggle('active');
    });

    // Handle redirect after login
    if (sessionStorage.getItem('redirectUrl') && localStorage.getItem('token')) {
      sessionStorage.removeItem('redirectUrl');
      location.reload();
    }

    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add loading states for buttons
    function addLoadingState(button) {
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
      button.disabled = true;
      
      return function() {
        button.innerHTML = originalText;
        button.disabled = false;
      };
    }

    // Enhanced error handling
    window.addEventListener('unhandledrejection', function(event) {
      console.error('Unhandled promise rejection:', event.reason);
      showError('حدث خطأ غير متوقع. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
    });

    // Add intersection observer for animations
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      });

      // Observe elements with animation class
      document.querySelectorAll('.curriculum-item, .feature-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
      });
    }
  </script>
</body>
</html>