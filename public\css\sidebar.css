/* Responsive Sidebar Component - Arabic RTL */

:root {
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 70px;
  --sidebar-bg: rgba(15, 23, 42, 0.95);
  --sidebar-border: rgba(59, 130, 246, 0.2);
  --sidebar-item-hover: rgba(59, 130, 246, 0.1);
  --sidebar-item-active: rgba(59, 130, 246, 0.2);
}

/* Sidebar Container */
.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--sidebar-bg);
  backdrop-filter: blur(20px);
  border-left: 1px solid var(--sidebar-border);
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--sidebar-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-100);
  text-decoration: none;
  font-weight: 700;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.sidebar-brand i {
  font-size: 1.5rem;
  color: var(--primary-light);
}

.sidebar-brand-text {
  transition: all 0.3s ease;
}

.sidebar.collapsed .sidebar-brand-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--gray-300);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: var(--sidebar-item-hover);
  color: var(--primary-light);
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav-section {
  margin-bottom: 2rem;
}

.sidebar-nav-title {
  padding: 0.5rem 1.5rem;
  color: var(--gray-400);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
}

.sidebar.collapsed .sidebar-nav-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.sidebar-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav-item {
  margin: 0.25rem 0;
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: var(--gray-300);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 0 25px 25px 0;
  margin-left: 1rem;
}

.sidebar-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-light);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar-nav-link:hover {
  background: var(--sidebar-item-hover);
  color: var(--gray-100);
  transform: translateX(-5px);
}

.sidebar-nav-link:hover::before {
  transform: scaleY(1);
}

.sidebar-nav-link.active {
  background: var(--sidebar-item-active);
  color: var(--primary-light);
  font-weight: 600;
}

.sidebar-nav-link.active::before {
  transform: scaleY(1);
}

.sidebar-nav-icon {
  font-size: 1.25rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.sidebar-nav-text {
  transition: all 0.3s ease;
  white-space: nowrap;
}

.sidebar.collapsed .sidebar-nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-nav-link {
  justify-content: center;
  padding: 1rem;
  margin: 0.25rem;
  border-radius: 12px;
}

/* Sidebar Footer */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  border-top: 1px solid var(--sidebar-border);
  background: rgba(15, 23, 42, 0.8);
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-300);
  transition: all 0.3s ease;
}

.sidebar-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--sidebar-border);
  flex-shrink: 0;
}

.sidebar-user-info {
  flex: 1;
  transition: all 0.3s ease;
}

.sidebar-user-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gray-100);
  display: block;
}

.sidebar-user-role {
  font-size: 0.75rem;
  color: var(--gray-400);
}

.sidebar.collapsed .sidebar-user-info {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Mobile Sidebar Toggle Button */
.mobile-sidebar-toggle {
  display: none;
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--primary-light);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.mobile-sidebar-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Content Adjustment */
.content-with-sidebar {
  transition: margin-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive Design */
@media (min-width: 1200px) {
  .sidebar {
    transform: translateX(0);
    position: relative;
  }
  
  .content-with-sidebar {
    margin-right: var(--sidebar-width);
  }
  
  .content-with-sidebar.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
  }
}

@media (max-width: 1199px) {
  .mobile-sidebar-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .sidebar {
    z-index: 1002;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    max-width: 320px;
  }
  
  .mobile-sidebar-toggle {
    top: 15px;
    right: 15px;
    width: 45px;
    height: 45px;
  }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
