// Enhanced Profile page functionality - Arabic RTL
document.addEventListener('DOMContentLoaded', () => {
  // Check if user is authenticated
  if (!requireAuth()) {
    return;
  }

  // Check user role and redirect if needed
  checkUserRoleAndRedirect();

  // Initialize profile components
  initializeProfileComponents();

  // Load user data
  loadUserProfile();
  loadNavbarProfile();
  loadUserStats();

  // Set up form handlers
  setupFormHandlers();

  // Enhanced animations and UI
  initializeEnhancedUI();

  // Setup logout
  document.getElementById('logoutBtn').addEventListener('click', logout);

  // Initialize page animations
  initializePageAnimations();
});

// Strict access control for profile page
async function checkUserRoleAndRedirect() {
  try {
    const response = await fetch('/api/auth/me', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      const user = data.data;

      // Check URL parameters for allowed access
      const urlParams = new URLSearchParams(window.location.search);
      const allowedAccess = urlParams.get('access') === 'allowed';
      const userRole = urlParams.get('role');

      // Verify that the URL role matches the actual user role
      const isValidAccess = allowedAccess && userRole === user.role;

      if (!isValidAccess) {
        // Redirect based on user role
        if (user.role === 'admin') {
          window.location.href = '/admin-dashboard.html';
          return;
        } else if (user.role === 'user') {
          window.location.href = '/courses.html';
          return;
        }
      }

      // If access is valid, continue loading the profile
      console.log('✅ Profile access granted for:', user.role);

    } else {
      // If not authenticated, redirect to login
      window.location.href = '/login.html';
    }
  } catch (error) {
    console.error('Error checking user role:', error);
    // On error, redirect to login for security
    window.location.href = '/login.html';
  }
}

// Initialize profile components
function initializeProfileComponents() {
  // Add loading states to components
  addLoadingStates();

  // Initialize component interactions
  initializeComponentInteractions();

  // Setup responsive behavior
  setupResponsiveBehavior();
}

// Add loading states to components
function addLoadingStates() {
  const profileCard = document.querySelector('.profile-card');
  const profileSettings = document.querySelector('.profile-settings');

  if (profileCard) {
    profileCard.classList.add('loading');
  }

  if (profileSettings) {
    profileSettings.classList.add('loading');
  }
}

// Initialize component interactions
function initializeComponentInteractions() {
  // Profile image click to switch to image tab
  const profileImageContainer = document.querySelector('.profile-image-container');
  if (profileImageContainer) {
    profileImageContainer.addEventListener('click', () => {
      const imageTab = document.getElementById('image-tab');
      if (imageTab) {
        imageTab.click();
      }
    });
  }

  // Enhanced tab switching with animations
  document.querySelectorAll('[data-toggle="tab"]').forEach(tab => {
    tab.addEventListener('click', (e) => {
      e.preventDefault();
      switchTabWithAnimation(tab);
    });
  });
}

// Setup responsive behavior
function setupResponsiveBehavior() {
  // Handle mobile navigation
  const handleResize = () => {
    const isMobile = window.innerWidth <= 768;
    const profileHeader = document.querySelector('.profile-header');

    if (profileHeader) {
      if (isMobile) {
        profileHeader.classList.add('mobile');
      } else {
        profileHeader.classList.remove('mobile');
      }
    }
  };

  window.addEventListener('resize', handleResize);
  handleResize(); // Initial call
}

// Load user profile data
async function loadUserProfile() {
  try {
    showLoading();

    const response = await fetch('/api/auth/me', {
      headers: getAuthHeaders()
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحميل الملف الشخصي');
    }

    const user = data.data;
    displayUserProfile(user);
    populateUpdateForm(user);

    // Update stored user data
    setAuthData(localStorage.getItem('token'), user);

  } catch (error) {
    console.error('Error loading profile:', error);
    showAlert(error.message || 'فشل في تحميل الملف الشخصي', 'danger');
  } finally {
    hideLoading();
  }
}

// Load navbar profile
async function loadNavbarProfile() {
  try {
    const response = await fetch('/api/auth/me', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      const user = data.data;

      document.getElementById('navUserName').textContent = user.name;
      document.getElementById('navUserRole').textContent = user.role === 'admin' ? 'مدير' : 'مستخدم';

      if (user.profileImage && user.profileImage !== 'no-photo.jpg') {
        document.getElementById('navProfileImage').src = user.profileImage;
      }

      // Update dashboard link based on user role
      const dashboardLink = document.getElementById('dashboardLink');
      const dashboardText = document.getElementById('dashboardText');

      if (user.role === 'admin') {
        dashboardLink.href = '/admin-dashboard.html';
        dashboardText.textContent = 'لوحة التحكم';
        dashboardLink.querySelector('i').className = 'fas fa-tachometer-alt';
      } else if (user.role === 'user') {
        dashboardLink.href = '/courses.html';
        dashboardText.textContent = 'الدورات';
        dashboardLink.querySelector('i').className = 'fas fa-book';
      }
    }
  } catch (error) {
    console.error('Error loading navbar profile:', error);
  }
}

// Load user statistics
async function loadUserStats() {
  try {
    const response = await fetch('/api/enrollments', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      const enrollments = data.data || [];

      const completedCount = enrollments.filter(e => e.progress >= 100).length;

      document.getElementById('enrollmentCount').textContent = enrollments.length;
      document.getElementById('completedCount').textContent = completedCount;
    }
  } catch (error) {
    console.error('Error loading user stats:', error);
    document.getElementById('enrollmentCount').textContent = '0';
    document.getElementById('completedCount').textContent = '0';
  }
}

// Display user profile information with enhanced animations
function displayUserProfile(user) {
  // Remove loading states
  const profileCard = document.querySelector('.profile-card');
  const profileSettings = document.querySelector('.profile-settings');

  if (profileCard) {
    profileCard.classList.remove('loading');
    profileCard.classList.add('loaded');
  }

  if (profileSettings) {
    profileSettings.classList.remove('loading');
    profileSettings.classList.add('loaded');
  }

  // Update profile information with animations
  updateElementWithAnimation('profileName', user.name);
  updateElementWithAnimation('profileEmail', user.email);
  updateElementWithAnimation('profileRole', user.role === 'admin' ? 'مدير' : 'مستخدم');

  // Update profile images
  const profileImage = document.getElementById('profileImage');
  const imagePreview = document.getElementById('imagePreview');

  const imageSrc = user.profileImage || '/uploads/no-photo.jpg';

  if (profileImage) {
    profileImage.style.opacity = '0';
    setTimeout(() => {
      profileImage.src = imageSrc;
      profileImage.style.opacity = '1';
    }, 200);
  }

  if (imagePreview) {
    imagePreview.src = imageSrc;
  }
}

// Update element with fade animation
function updateElementWithAnimation(elementId, newText) {
  const element = document.getElementById(elementId);
  if (element) {
    element.style.opacity = '0';
    setTimeout(() => {
      element.textContent = newText;
      element.style.opacity = '1';
    }, 200);
  }
}

// Populate update form with current data
function populateUpdateForm(user) {
  document.getElementById('updateName').value = user.name;
  document.getElementById('updateEmail').value = user.email;
}

// Set up form event handlers
function setupFormHandlers() {
  // Update details form
  const updateDetailsForm = document.getElementById('updateDetailsForm');
  updateDetailsForm.addEventListener('submit', handleUpdateDetails);

  // Update password form
  const updatePasswordForm = document.getElementById('updatePasswordForm');
  updatePasswordForm.addEventListener('submit', handleUpdatePassword);

  // Update image form
  const updateImageForm = document.getElementById('updateImageForm');
  updateImageForm.addEventListener('submit', handleUpdateImage);

  // Enhanced tab switching is handled in initializeComponentInteractions
}

// Handle update details
async function handleUpdateDetails(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const updateData = {
    name: formData.get('name'),
    email: formData.get('email')
  };

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    const response = await fetch('/api/auth/updatedetails', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      },
      body: JSON.stringify(updateData)
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث البيانات');
    }

    // Update stored user data
    setAuthData(localStorage.getItem('token'), data.data);

    // Update display
    displayUserProfile(data.data);
    loadNavbarProfile();

    showAlert('تم تحديث البيانات بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating details:', error);
    showAlert(error.message || 'فشل في تحديث البيانات', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
  }
}

// Handle update password
async function handleUpdatePassword(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const newPassword = formData.get('newPassword');
  const confirmNewPassword = formData.get('confirmNewPassword');

  // Validate password confirmation
  if (newPassword !== confirmNewPassword) {
    showAlert('كلمات المرور الجديدة غير متطابقة', 'danger');
    return;
  }

  const updateData = {
    currentPassword: formData.get('currentPassword'),
    newPassword: newPassword
  };

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

    const response = await fetch('/api/auth/updatepassword', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      },
      body: JSON.stringify(updateData)
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث كلمة المرور');
    }

    // Update stored token (new token is returned)
    setAuthData(data.token, data.data);

    // Reset form
    e.target.reset();

    showAlert('تم تحديث كلمة المرور بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating password:', error);
    showAlert(error.message || 'فشل في تحديث كلمة المرور', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تغيير كلمة المرور';
  }
}

// Handle update profile image
async function handleUpdateImage(e) {
  e.preventDefault();

  const formData = new FormData(e.target);

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';

    const response = await fetch('/api/auth/updateprofileimage', {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: formData
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث الصورة الشخصية');
    }

    // Update stored user data
    setAuthData(localStorage.getItem('token'), data.data);

    // Update display
    displayUserProfile(data.data);
    loadNavbarProfile();

    // Reset form
    e.target.reset();

    showAlert('تم تحديث الصورة الشخصية بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating profile image:', error);
    showAlert(error.message || 'فشل في تحديث الصورة الشخصية', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-upload"></i> تحديث الصورة';
  }
}

// Utility functions
function showLoading() {
  document.getElementById('loadingSpinner').style.display = 'block';
}

function hideLoading() {
  document.getElementById('loadingSpinner').style.display = 'none';
}

function logout() {
  if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
    clearAuthData();
    window.location.href = '/login.html';
  }
}

// Initialize enhanced UI features
function initializeEnhancedUI() {
  // Password confirmation validation
  const newPassword = document.getElementById('newPassword');
  const confirmPassword = document.getElementById('confirmNewPassword');

  if (newPassword && confirmPassword) {
    confirmPassword.addEventListener('input', function() {
      if (this.value !== newPassword.value) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
      } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
      }
    });
  }

  // Enhanced file upload preview
  const fileInput = document.getElementById('profileImageFile');
  if (fileInput) {
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          showAlert('يجب أن يكون حجم الملف أقل من 5 ميجابايت', 'danger');
          this.value = '';
          return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
          showAlert('يرجى اختيار ملف صورة صحيح', 'danger');
          this.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          const profileImg = document.getElementById('profileImage');
          const imagePreview = document.getElementById('imagePreview');

          if (profileImg) {
            profileImg.src = e.target.result;
            profileImg.style.transform = 'scale(1.05)';
            setTimeout(() => {
              profileImg.style.transform = 'scale(1)';
            }, 300);
          }

          if (imagePreview) {
            imagePreview.src = e.target.result;
          }
        };
        reader.readAsDataURL(file);
      }
    });
  }

  // Enhanced image error handling
  const profileImage = document.getElementById('profileImage');
  if (profileImage) {
    profileImage.addEventListener('error', function() {
      this.src = '/uploads/no-photo.jpg';
    });
  }

  // Form validation enhancements
  document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('blur', function() {
      if (this.checkValidity()) {
        this.classList.add('is-valid');
        this.classList.remove('is-invalid');
      } else {
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
      }
    });
  });
}

// Show alert message
function showAlert(message, type = 'info') {
  // Create alert element
  const alert = document.createElement('div');
  alert.className = `alert alert-${type}`;
  alert.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    max-width: 400px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    animation: slideInDown 0.3s ease-out;
  `;

  // Set background color based on type
  switch (type) {
    case 'success':
      alert.style.backgroundColor = '#10b981';
      break;
    case 'danger':
      alert.style.backgroundColor = '#ef4444';
      break;
    case 'warning':
      alert.style.backgroundColor = '#f59e0b';
      break;
    default:
      alert.style.backgroundColor = '#3b82f6';
  }

  alert.innerHTML = `
    ${message}
    <button type="button" class="close" style="background: none; border: none; color: white; font-size: 1.25rem; cursor: pointer; margin-right: 1rem; opacity: 0.8;">
      &times;
    </button>
  `;

  // Add close functionality
  alert.querySelector('.close').addEventListener('click', () => {
    alert.style.animation = 'slideOutUp 0.3s ease-out';
    setTimeout(() => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    }, 300);
  });

  document.body.appendChild(alert);

  // Remove alert after 5 seconds
  setTimeout(() => {
    if (alert.parentNode) {
      alert.style.animation = 'slideOutUp 0.3s ease-out';
      setTimeout(() => {
        if (alert.parentNode) {
          alert.parentNode.removeChild(alert);
        }
      }, 300);
    }
  }, 5000);
}

// Enhanced tab switching with smooth animations
function switchTabWithAnimation(tab) {
  const allTabs = document.querySelectorAll('.nav-link');
  const allPanes = document.querySelectorAll('.tab-pane');
  const targetPane = document.querySelector(tab.getAttribute('href'));

  // Add switching class to prevent multiple clicks
  document.body.classList.add('tab-switching');

  // Animate out current active pane
  const currentActivePane = document.querySelector('.tab-pane.show.active');
  if (currentActivePane) {
    currentActivePane.style.opacity = '0';
    currentActivePane.style.transform = 'translateY(-20px)';
  }

  // Remove active from all tabs with stagger effect
  allTabs.forEach((link, index) => {
    setTimeout(() => {
      link.classList.remove('active');
      if (link !== tab) {
        link.style.transform = 'translateY(0) scale(0.95)';
        link.style.opacity = '0.7';
      }
    }, index * 50);
  });

  // Remove active from all panes
  setTimeout(() => {
    allPanes.forEach(pane => {
      pane.classList.remove('show', 'active');
    });
  }, 200);

  // Activate clicked tab with enhanced animation
  setTimeout(() => {
    tab.classList.add('active');
    tab.style.transform = 'translateY(-3px) scale(1.02)';
    tab.style.opacity = '1';

    // Reset other tabs
    allTabs.forEach(link => {
      if (link !== tab) {
        link.style.transform = 'translateY(0) scale(1)';
        link.style.opacity = '1';
      }
    });
  }, 250);

  // Show target pane with smooth animation
  if (targetPane) {
    setTimeout(() => {
      targetPane.classList.add('show', 'active');
      targetPane.style.opacity = '1';
      targetPane.style.transform = 'translateY(0)';

      // Remove switching class
      document.body.classList.remove('tab-switching');
    }, 400);
  }

  // Add ripple effect to clicked tab
  createRippleEffect(tab);
}

// Create ripple effect for tab clicks
function createRippleEffect(element) {
  const ripple = document.createElement('div');
  ripple.className = 'tab-ripple';

  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);

  ripple.style.width = ripple.style.height = size + 'px';
  ripple.style.left = '50%';
  ripple.style.top = '50%';
  ripple.style.transform = 'translate(-50%, -50%) scale(0)';

  element.style.position = 'relative';
  element.appendChild(ripple);

  // Animate ripple
  setTimeout(() => {
    ripple.style.transform = 'translate(-50%, -50%) scale(1)';
    ripple.style.opacity = '0';
  }, 10);

  // Remove ripple after animation
  setTimeout(() => {
    if (ripple.parentNode) {
      ripple.parentNode.removeChild(ripple);
    }
  }, 600);
}

// Initialize page animations
function initializePageAnimations() {
  // Animate profile card on load
  const profileCard = document.querySelector('.profile-card');
  if (profileCard) {
    profileCard.style.opacity = '0';
    profileCard.style.transform = 'translateY(30px)';

    setTimeout(() => {
      profileCard.style.transition = 'all 0.6s ease';
      profileCard.style.opacity = '1';
      profileCard.style.transform = 'translateY(0)';
    }, 200);
  }

  // Animate profile settings
  const profileSettings = document.querySelector('.profile-settings');
  if (profileSettings) {
    profileSettings.style.opacity = '0';
    profileSettings.style.transform = 'translateY(30px)';

    setTimeout(() => {
      profileSettings.style.transition = 'all 0.6s ease';
      profileSettings.style.opacity = '1';
      profileSettings.style.transform = 'translateY(0)';
    }, 400);
  }

  // Animate stats with stagger effect
  const statItems = document.querySelectorAll('.stat-item');
  statItems.forEach((item, index) => {
    item.style.opacity = '0';
    item.style.transform = 'translateY(20px)';

    setTimeout(() => {
      item.style.transition = 'all 0.4s ease';
      item.style.opacity = '1';
      item.style.transform = 'translateY(0)';
    }, 600 + (index * 100));
  });
}

// Enhanced form validation with real-time feedback
function enhanceFormValidation() {
  const forms = document.querySelectorAll('form');

  forms.forEach(form => {
    const inputs = form.querySelectorAll('.form-control');

    inputs.forEach(input => {
      // Real-time validation
      input.addEventListener('input', function() {
        validateInputRealTime(this);
      });

      // Enhanced focus effects
      input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
      });

      input.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
        validateInputRealTime(this);
      });
    });
  });
}

// Real-time input validation
function validateInputRealTime(input) {
  const isValid = input.checkValidity();
  const formGroup = input.parentElement;

  // Remove existing validation classes
  input.classList.remove('is-valid', 'is-invalid');
  formGroup.classList.remove('has-success', 'has-error');

  if (input.value.length > 0) {
    if (isValid) {
      input.classList.add('is-valid');
      formGroup.classList.add('has-success');
    } else {
      input.classList.add('is-invalid');
      formGroup.classList.add('has-error');
    }
  }
}

// Initialize enhanced UI features on load
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    enhanceFormValidation();
  }, 1000);
});