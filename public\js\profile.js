// Profile page functionality - Arabic RTL
document.addEventListener('DOMContentLoaded', () => {
  // Check if user is authenticated
  if (!requireAuth()) {
    return;
  }

  // Load user profile and navigation
  loadUserProfile();
  loadNavbarProfile();
  loadUserStats();

  // Set up form handlers
  setupFormHandlers();

  // Enhanced animations and UI
  initializeEnhancedUI();

  // Setup logout
  document.getElementById('logoutBtn').addEventListener('click', logout);
});

// Load user profile data
async function loadUserProfile() {
  try {
    showLoading();

    const response = await fetch('/api/auth/me', {
      headers: getAuthHeaders()
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحميل الملف الشخصي');
    }

    const user = data.data;
    displayUserProfile(user);
    populateUpdateForm(user);

    // Update stored user data
    setAuthData(localStorage.getItem('token'), user);

  } catch (error) {
    console.error('Error loading profile:', error);
    showAlert(error.message || 'فشل في تحميل الملف الشخصي', 'danger');
  } finally {
    hideLoading();
  }
}

// Load navbar profile
async function loadNavbarProfile() {
  try {
    const response = await fetch('/api/auth/me', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      const user = data.data;

      document.getElementById('navUserName').textContent = user.name;
      document.getElementById('navUserRole').textContent = user.role === 'admin' ? 'مدير' : 'مستخدم';

      if (user.profileImage && user.profileImage !== 'no-photo.jpg') {
        document.getElementById('navProfileImage').src = user.profileImage;
      }
    }
  } catch (error) {
    console.error('Error loading navbar profile:', error);
  }
}

// Load user statistics
async function loadUserStats() {
  try {
    const response = await fetch('/api/enrollments', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      const enrollments = data.data || [];

      const completedCount = enrollments.filter(e => e.progress >= 100).length;

      document.getElementById('enrollmentCount').textContent = enrollments.length;
      document.getElementById('completedCount').textContent = completedCount;
    }
  } catch (error) {
    console.error('Error loading user stats:', error);
    document.getElementById('enrollmentCount').textContent = '0';
    document.getElementById('completedCount').textContent = '0';
  }
}

// Display user profile information
function displayUserProfile(user) {
  document.getElementById('profileName').textContent = user.name;
  document.getElementById('profileEmail').textContent = user.email;
  document.getElementById('profileRole').textContent = user.role === 'admin' ? 'مدير' : 'مستخدم';

  const profileImage = document.getElementById('profileImage');
  const imagePreview = document.getElementById('imagePreview');

  const imageSrc = user.profileImage || '/uploads/no-photo.jpg';
  profileImage.src = imageSrc;
  if (imagePreview) imagePreview.src = imageSrc;
}

// Populate update form with current data
function populateUpdateForm(user) {
  document.getElementById('updateName').value = user.name;
  document.getElementById('updateEmail').value = user.email;
}

// Set up form event handlers
function setupFormHandlers() {
  // Update details form
  const updateDetailsForm = document.getElementById('updateDetailsForm');
  updateDetailsForm.addEventListener('submit', handleUpdateDetails);

  // Update password form
  const updatePasswordForm = document.getElementById('updatePasswordForm');
  updatePasswordForm.addEventListener('submit', handleUpdatePassword);

  // Update image form
  const updateImageForm = document.getElementById('updateImageForm');
  updateImageForm.addEventListener('submit', handleUpdateImage);

  // Tab switching with Bootstrap-like functionality
  document.querySelectorAll('[data-toggle="tab"]').forEach(tab => {
    tab.addEventListener('click', (e) => {
      e.preventDefault();

      // Remove active from all tabs and panes
      document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
      document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('show', 'active');
      });

      // Add active to clicked tab
      tab.classList.add('active');

      // Show target pane
      const target = document.querySelector(tab.getAttribute('href'));
      if (target) {
        target.classList.add('show', 'active');
      }
    });
  });
}

// Handle update details
async function handleUpdateDetails(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const updateData = {
    name: formData.get('name'),
    email: formData.get('email')
  };

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    const response = await fetch('/api/auth/updatedetails', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      },
      body: JSON.stringify(updateData)
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث البيانات');
    }

    // Update stored user data
    setAuthData(localStorage.getItem('token'), data.data);

    // Update display
    displayUserProfile(data.data);
    loadNavbarProfile();

    showAlert('تم تحديث البيانات بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating details:', error);
    showAlert(error.message || 'فشل في تحديث البيانات', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
  }
}

// Handle update password
async function handleUpdatePassword(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const newPassword = formData.get('newPassword');
  const confirmNewPassword = formData.get('confirmNewPassword');

  // Validate password confirmation
  if (newPassword !== confirmNewPassword) {
    showAlert('كلمات المرور الجديدة غير متطابقة', 'danger');
    return;
  }

  const updateData = {
    currentPassword: formData.get('currentPassword'),
    newPassword: newPassword
  };

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

    const response = await fetch('/api/auth/updatepassword', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      },
      body: JSON.stringify(updateData)
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث كلمة المرور');
    }

    // Update stored token (new token is returned)
    setAuthData(data.token, data.data);

    // Reset form
    e.target.reset();

    showAlert('تم تحديث كلمة المرور بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating password:', error);
    showAlert(error.message || 'فشل في تحديث كلمة المرور', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تغيير كلمة المرور';
  }
}

// Handle update profile image
async function handleUpdateImage(e) {
  e.preventDefault();

  const formData = new FormData(e.target);

  try {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';

    const response = await fetch('/api/auth/updateprofileimage', {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: formData
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'فشل في تحديث الصورة الشخصية');
    }

    // Update stored user data
    setAuthData(localStorage.getItem('token'), data.data);

    // Update display
    displayUserProfile(data.data);
    loadNavbarProfile();

    // Reset form
    e.target.reset();

    showAlert('تم تحديث الصورة الشخصية بنجاح!', 'success');

  } catch (error) {
    console.error('Error updating profile image:', error);
    showAlert(error.message || 'فشل في تحديث الصورة الشخصية', 'danger');
  } finally {
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
    submitBtn.innerHTML = '<i class="fas fa-upload"></i> تحديث الصورة';
  }
}

// Utility functions
function showLoading() {
  document.getElementById('loadingSpinner').style.display = 'block';
}

function hideLoading() {
  document.getElementById('loadingSpinner').style.display = 'none';
}

function logout() {
  if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
    clearAuthData();
    window.location.href = '/login.html';
  }
}

// Initialize enhanced UI features
function initializeEnhancedUI() {
  // Password confirmation validation
  const newPassword = document.getElementById('newPassword');
  const confirmPassword = document.getElementById('confirmNewPassword');

  if (newPassword && confirmPassword) {
    confirmPassword.addEventListener('input', function() {
      if (this.value !== newPassword.value) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
      } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
      }
    });
  }

  // Enhanced file upload preview
  const fileInput = document.getElementById('profileImageFile');
  if (fileInput) {
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          showAlert('يجب أن يكون حجم الملف أقل من 5 ميجابايت', 'danger');
          this.value = '';
          return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
          showAlert('يرجى اختيار ملف صورة صحيح', 'danger');
          this.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          const profileImg = document.getElementById('profileImage');
          const imagePreview = document.getElementById('imagePreview');

          if (profileImg) {
            profileImg.src = e.target.result;
            profileImg.style.transform = 'scale(1.05)';
            setTimeout(() => {
              profileImg.style.transform = 'scale(1)';
            }, 300);
          }

          if (imagePreview) {
            imagePreview.src = e.target.result;
          }
        };
        reader.readAsDataURL(file);
      }
    });
  }

  // Enhanced image error handling
  const profileImage = document.getElementById('profileImage');
  if (profileImage) {
    profileImage.addEventListener('error', function() {
      this.src = '/uploads/no-photo.jpg';
    });
  }

  // Form validation enhancements
  document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('blur', function() {
      if (this.checkValidity()) {
        this.classList.add('is-valid');
        this.classList.remove('is-invalid');
      } else {
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
      }
    });
  });
}

// Show alert message
function showAlert(message, type = 'info') {
  // Create alert element
  const alert = document.createElement('div');
  alert.className = `alert alert-${type}`;
  alert.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    max-width: 400px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    animation: slideInDown 0.3s ease-out;
  `;

  // Set background color based on type
  switch (type) {
    case 'success':
      alert.style.backgroundColor = '#10b981';
      break;
    case 'danger':
      alert.style.backgroundColor = '#ef4444';
      break;
    case 'warning':
      alert.style.backgroundColor = '#f59e0b';
      break;
    default:
      alert.style.backgroundColor = '#3b82f6';
  }

  alert.innerHTML = `
    ${message}
    <button type="button" class="close" style="background: none; border: none; color: white; font-size: 1.25rem; cursor: pointer; margin-right: 1rem; opacity: 0.8;">
      &times;
    </button>
  `;

  // Add close functionality
  alert.querySelector('.close').addEventListener('click', () => {
    alert.style.animation = 'slideOutUp 0.3s ease-out';
    setTimeout(() => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    }, 300);
  });

  document.body.appendChild(alert);

  // Remove alert after 5 seconds
  setTimeout(() => {
    if (alert.parentNode) {
      alert.style.animation = 'slideOutUp 0.3s ease-out';
      setTimeout(() => {
        if (alert.parentNode) {
          alert.parentNode.removeChild(alert);
        }
      }, 300);
    }
  }, 5000);
}