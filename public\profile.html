<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الملف الشخصي - منصة دوراتي</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/profile.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <a href="/" class="navbar-brand">
        <i class="fas fa-graduation-cap"></i>
        منصة دوراتي
      </a>

      <div class="nav-user">
        <div class="user-info">
          <img id="navProfileImage" src="/uploads/no-photo.jpg" class="nav-profile-image" alt="الصورة الشخصية">
          <div class="user-details">
            <span id="navUserName">مرحباً</span>
            <small id="navUserRole">مستخدم</small>
          </div>
        </div>
        <div class="nav-actions">
          <a href="admin-dashboard.html" class="nav-link">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
          </a>
          <button id="logoutBtn" class="nav-link logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            تسجيل الخروج
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Container -->
  <div class="profile-container">
    <div class="container">
      <div class="profile-header">
        <h1 class="profile-title">الملف الشخصي</h1>
        <p class="profile-subtitle">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
      </div>

      <div class="row">
        <!-- Profile Card -->
        <div class="col-lg-4 col-md-5">
          <div class="profile-card">
            <div class="profile-image-container">
              <img id="profileImage" src="/uploads/no-photo.jpg" class="profile-image" alt="الصورة الشخصية">
              <div class="profile-image-overlay">
                <i class="fas fa-camera"></i>
              </div>
            </div>
            <h2 id="profileName" class="profile-name">جاري التحميل...</h2>
            <p id="profileEmail" class="profile-email">جاري التحميل...</p>
            <span id="profileRole" class="profile-role">جاري التحميل...</span>

            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-number" id="enrollmentCount">0</div>
                <div class="stat-label">الدورات المسجلة</div>
              </div>
              <div class="stat-item">
                <div class="stat-number" id="completedCount">0</div>
                <div class="stat-label">الدورات المكتملة</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Settings -->
        <div class="col-lg-8 col-md-7">
          <div class="profile-settings">
            <div class="profile-tabs">
              <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                <li class="nav-item">
                  <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab">
                    <i class="fas fa-user"></i>
                    تحديث البيانات
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="password-tab" data-toggle="tab" href="#password" role="tab">
                    <i class="fas fa-lock"></i>
                    تغيير كلمة المرور
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="image-tab" data-toggle="tab" href="#image" role="tab">
                    <i class="fas fa-image"></i>
                    الصورة الشخصية
                  </a>
                </li>
              </ul>
            </div>

            <div class="tab-content" id="profileTabContent">
              <!-- Update Details Tab -->
              <div class="tab-pane fade show active" id="details" role="tabpanel">
                <div class="profile-section-card">
                  <h2>تحديث البيانات الشخصية</h2>
                  <form id="updateDetailsForm">
                    <div class="form-group">
                      <label for="updateName">
                        <i class="fas fa-user"></i>
                        الاسم الكامل
                      </label>
                      <input type="text" class="form-control" id="updateName" name="name" placeholder="أدخل اسمك الكامل" required>
                    </div>

                    <div class="form-group">
                      <label for="updateEmail">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني
                      </label>
                      <input type="email" class="form-control" id="updateEmail" name="email" placeholder="أدخل بريدك الإلكتروني" required>
                    </div>

                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-save"></i>
                      حفظ التغييرات
                    </button>
                  </form>
                </div>
              </div>

              <!-- Change Password Tab -->
              <div class="tab-pane fade" id="password" role="tabpanel">
                <div class="profile-section-card">
                  <h2>تغيير كلمة المرور</h2>
                  <form id="updatePasswordForm">
                    <div class="form-group">
                      <label for="currentPassword">
                        <i class="fas fa-lock"></i>
                        كلمة المرور الحالية
                      </label>
                      <input type="password" class="form-control" id="currentPassword" name="currentPassword" placeholder="أدخل كلمة المرور الحالية" required>
                    </div>

                    <div class="form-group">
                      <label for="newPassword">
                        <i class="fas fa-key"></i>
                        كلمة المرور الجديدة
                      </label>
                      <input type="password" class="form-control" id="newPassword" name="newPassword" placeholder="أدخل كلمة المرور الجديدة" required minlength="6">
                      <small class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                    </div>

                    <div class="form-group">
                      <label for="confirmNewPassword">
                        <i class="fas fa-check"></i>
                        تأكيد كلمة المرور الجديدة
                      </label>
                      <input type="password" class="form-control" id="confirmNewPassword" name="confirmNewPassword" placeholder="أعد إدخال كلمة المرور الجديدة" required>
                    </div>

                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-shield-alt"></i>
                      تغيير كلمة المرور
                    </button>
                  </form>
                </div>
              </div>

              <!-- Profile Image Tab -->
              <div class="tab-pane fade" id="image" role="tabpanel">
                <div class="profile-section-card">
                  <h2>تحديث الصورة الشخصية</h2>
                  <form id="updateImageForm" enctype="multipart/form-data">
                    <div class="image-upload-area">
                      <div class="upload-preview">
                        <img id="imagePreview" src="/uploads/no-photo.jpg" alt="معاينة الصورة">
                      </div>
                      <div class="upload-controls">
                        <label for="profileImageFile" class="upload-label">
                          <i class="fas fa-cloud-upload-alt"></i>
                          اختر صورة جديدة
                        </label>
                        <input type="file" class="form-control-file" id="profileImageFile" name="profileImage" accept="image/*" required>
                        <small class="form-text">يُسمح بملفات JPG, PNG, GIF بحد أقصى 5 ميجابايت</small>
                      </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-upload"></i>
                      تحديث الصورة
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <i class="fas fa-graduation-cap"></i>
          منصة دوراتي
        </div>
        <p>&copy; 2024 منصة دوراتي. جميع الحقوق محفوظة.</p>
      </div>
    </div>
  </footer>

  <!-- Loading Spinner -->
  <div id="loadingSpinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Scripts -->
  <script src="/js/auth.js"></script>
  <script src="/js/profile.js"></script>
</body>
</html>
