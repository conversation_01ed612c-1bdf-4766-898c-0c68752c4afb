const cloudinary = require('cloudinary').v2;
require('dotenv').config();

// ✅ Validate required environment variables
const requiredEnvVars = [
  'CLOUDINARY_CLOUD_NAME',
  'CLOUDINARY_API_KEY',
  'CLOUDINARY_API_SECRET'
];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  throw new Error(
    `Missing required Cloudinary environment variables: ${missingVars.join(', ')}`
  );
}

// ✅ Configure Cloudinary with enhanced settings
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true, // Force HTTPS URLs
  upload_preset: 'courses_app' // Optional: set preset in Cloudinary dashboard
});

// ================== IMAGE UPLOAD ==================
const uploadImage = async (filePath, options = {}) => {
  const defaultOptions = {
    resource_type: 'image',
    quality: 'auto:good',
    fetch_format: 'auto',
    secure: true,
    transformation: [
      { width: 1280, crop: "limit" }, // prevent very large images
    ],
    ...options
  };

  try {
    const result = await cloudinary.uploader.upload(filePath, defaultOptions);
    return result;
  } catch (error) {
    console.error('❌ Cloudinary image upload error:', error);
    throw new Error('Failed to upload image to Cloudinary');
  }
};

// ================== VIDEO UPLOAD ==================
const uploadVideo = async (filePath, options = {}) => {
  const defaultOptions = {
    resource_type: 'video',
    quality: 'auto:good',
    secure: true,
    timeout: 900000, // ⏱️ 15 minutes timeout for very large videos
    chunk_size: 10 * 1024 * 1024, // 10MB chunks (better stability for big files)
    eager: [
      { width: 720, height: 480, crop: "limit", format: "mp4" }, // standard quality
      { width: 1280, height: 720, crop: "limit", format: "mp4" } // HD quality
    ],
    eager_async: true, // process video versions in background
    ...options
  };

  try {
    console.log('🔄 Starting video upload to Cloudinary...');
    const result = await cloudinary.uploader.upload(filePath, defaultOptions);
    console.log('✅ Video uploaded successfully to Cloudinary');
    return result;
  } catch (error) {
    console.error('❌ Cloudinary video upload error:', error);

    if (error.name === 'TimeoutError' || error.http_code === 499) {
      throw new Error('⏱️ فشل في رفع الفيديو - انتهت مهلة الاتصال. حاول مرة أخرى أو استخدم شبكة أفضل.');
    } else if (error.http_code === 400) {
      throw new Error('⚠️ فشل في رفع الفيديو - تنسيق الفيديو غير مدعوم أو الملف تالف.');
    } else if (error.http_code === 413) {
      throw new Error('🚫 فشل في رفع الفيديو - حجم الملف كبير جداً.');
    } else {
      throw new Error('❌ فشل في رفع الفيديو إلى Cloudinary');
    }
  }
};

// ================== DELETE RESOURCE ==================
const deleteResource = async (publicId, resourceType = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, { resource_type: resourceType });
    return result;
  } catch (error) {
    console.error('❌ Cloudinary delete error:', error);
    throw new Error('Failed to delete resource from Cloudinary');
  }
};

module.exports = {
  cloudinary,
  uploadImage,
  uploadVideo,
  deleteResource
};
