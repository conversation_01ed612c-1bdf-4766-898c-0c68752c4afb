// Courses page functionality
let currentPage = 1;
let currentFilters = {
  search: '',
  category: '',
  level: '',
  price: '',
  sort: 'newest'
};

// Helper functions for video count and duration
function getVideoCountText(count) {
  if (count === 0) return 'لا توجد فيديوهات';
  if (count === 1) return 'فيديو واحد';
  if (count === 2) return 'فيديوان';
  if (count >= 3 && count <= 10) return `${count} فيديوهات`;
  return `${count} فيديو`;
}

function calculateTotalDuration(videos) {
  if (!videos || !Array.isArray(videos)) return 0;
  
  return videos.reduce((total, video) => {
    return total + (video.duration || 0);
  }, 0);
}

function formatDuration(minutes) {
  if (!minutes || minutes < 1) return '';
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = Math.floor(minutes % 60);
  
  if (hours === 0) {
    return `${remainingMinutes} دقيقة`;
  } else if (hours === 1) {
    return remainingMinutes > 0 ? `ساعة و ${remainingMinutes} دقيقة` : 'ساعة واحدة';
  } else {
    return remainingMinutes > 0 ? `${hours} ساعات و ${remainingMinutes} دقيقة` : `${hours} ساعات`;
  }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  updateNavbar();
  loadAllCourses(); // Load all courses initially
  setupFilterListeners(); // Setup filter event listeners
});

// Update navbar based on authentication status
function updateNavbar() {
  const navAuth = document.getElementById('navAuth');
  const navLinks = document.querySelector('.nav-links');
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Add profile link for authenticated users with proper role
  if (token && (user.role === 'admin' || user.role === 'user')) {
    // Check if profile link already exists
    const existingProfileLink = navLinks.querySelector('.profile-link');
    if (!existingProfileLink) {
      const profileLi = document.createElement('li');
      profileLi.innerHTML = `<a href="profile.html?access=allowed&role=${user.role}" class="nav-link profile-link">الملف الشخصي</a>`;
      // Insert before "من نحن" link
      const aboutLink = navLinks.querySelector('a[href="#about"]').parentElement;
      navLinks.insertBefore(profileLi, aboutLink);
    }
  } else {
    // Remove profile link if user is not authenticated
    const existingProfileLink = navLinks.querySelector('.profile-link');
    if (existingProfileLink) {
      existingProfileLink.parentElement.remove();
    }
  }

  if (token && user.name) {
    navAuth.innerHTML = `
      <div class="user-menu">
        <span class="user-name">مرحباً، ${user.name}</span>
        ${user.role === 'admin' ?
          '<a href="/admin-dashboard.html" class="btn btn-secondary">لوحة التحكم</a>' :
          ''
        }
        <button onclick="logout()" class="btn btn-outline">تسجيل الخروج</button>
      </div>
    `;
  } else {
    navAuth.innerHTML = `
      <a href="/login.html" class="btn btn-secondary">تسجيل الدخول</a>
      <a href="/register.html" class="btn btn-primary">ابدأ الآن</a>
    `;
  }
}

// Setup filter event listeners
function setupFilterListeners() {
  // Search functionality with instant search
  const searchInput = document.getElementById('courseSearch');
  if (searchInput) {
    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        performSearch();
      }
    });

    // Instant search with debounce
    let searchTimeout;
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      const searchValue = searchInput.value.trim();
      
      // If search is cleared, reload all courses immediately
      if (searchValue === '') {
        loadAllCourses();
        return;
      }
      
      // Search after user stops typing for 300ms
      searchTimeout = setTimeout(() => {
        performSearch();
      }, 300);
    });
  }

  // Search button
  const searchBtn = document.getElementById('searchBtn');
  if (searchBtn) {
    searchBtn.addEventListener('click', performSearch);
  }

  // Filter dropdowns
  const categoryFilter = document.getElementById('categoryFilter');
  if (categoryFilter) {
    categoryFilter.addEventListener('change', filterCourses);
  }

  const levelFilter = document.getElementById('levelFilter');
  if (levelFilter) {
    levelFilter.addEventListener('change', filterCourses);
  }

  const priceFilter = document.getElementById('priceFilter');
  if (priceFilter) {
    priceFilter.addEventListener('change', filterCourses);
  }

  const sortFilter = document.getElementById('sortFilter');
  if (sortFilter) {
    sortFilter.addEventListener('change', filterCourses);
  }

  console.log('✅ Filter listeners setup complete');
}

// Logout function
function logout() {
  if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = '/';
  }
}

// Load all courses initially (without pagination)
async function loadAllCourses() {
  try {
    showLoading();

    console.log('🔄 Loading all courses initially...');

    // Load all published courses without pagination
    const response = await fetch('/api/courses?isPublished=true');

    if (response.ok) {
      const data = await response.json();
      console.log('📊 API Response:', data);
      
      displayCourses(data.data);
      updateResultsInfo(data.count, data.total || data.count);

      console.log(`✅ Loaded ${data.count} courses initially`);
    } else {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`Failed to load courses: ${response.status}`);
    }
  } catch (error) {
    console.error('Error loading all courses:', error);
    showError('حدث خطأ في تحميل الدورات: ' + error.message);
  } finally {
    hideLoading();
  }
}

// Load courses with filters and pagination
async function loadCourses(page = 1) {
  try {
    showLoading();
    
    // Build query parameters
    const params = new URLSearchParams({
      page: page,
      limit: 12,
      isPublished: 'true' // Only show published courses
    });
    
    // Enhanced search - search in title, description, instructor, and category
    if (currentFilters.search) {
      params.append('search', currentFilters.search);
      // Also search in specific fields for better results
      params.append('searchFields', 'title,description,instructor,category');
    }
    
    if (currentFilters.category) {
      params.append('category', currentFilters.category);
    }
    
    if (currentFilters.level) {
      params.append('level', currentFilters.level);
    }
    
    if (currentFilters.price === 'free') {
      params.append('price', '0');
    } else if (currentFilters.price === 'paid') {
      params.append('minPrice', '0.01');
    }
    
    if (currentFilters.sort) {
      switch (currentFilters.sort) {
        case 'newest':
          params.append('sort', '-createdAt');
          break;
        case 'oldest':
          params.append('sort', 'createdAt');
          break;
        case 'rating':
          params.append('sort', '-averageRating');
          break;
        case 'popular':
          params.append('sort', '-enrollmentCount');
          break;
        case 'price-low':
          params.append('sort', 'price');
          break;
        case 'price-high':
          params.append('sort', '-price');
          break;
        case 'relevance':
          // If searching, sort by relevance
          if (currentFilters.search) {
            params.append('sort', 'relevance');
          } else {
            params.append('sort', '-createdAt');
          }
          break;
      }
    } else if (currentFilters.search) {
      // Default to relevance when searching
      params.append('sort', 'relevance');
    }

    console.log('🔗 API Request:', `/api/courses?${params.toString()}`);

    const response = await fetch(`/api/courses?${params}`);
    
    if (response.ok) {
      const data = await response.json();
      displayCourses(data.data);
      displayPagination(data.pagination);
      updateCoursesCount(data.count, data.total);
      currentPage = page;
      
      // Update courses count - fixed function name
function updateResultsInfo(count, total) {
  const coursesCount = document.getElementById('coursesCount');
  if (coursesCount) {
    if (count === total) {
      coursesCount.textContent = `${total} دورة`;
    } else {
      coursesCount.textContent = `${count} من ${total} دورة`;
    }
  }
}

// Update courses count
function updateCoursesCount(count, total) {
  // Only use updateResultsInfo if not searching
  if (!currentFilters.search) {
    updateResultsInfo(count, total);
  }
}
      updateSearchResults(currentFilters.search, data.count, data.total);
    } else {
      throw new Error('Failed to load courses');
    }
  } catch (error) {
    console.error('Error loading courses:', error);
    showError('حدث خطأ في تحميل الدورات');
  } finally {
    hideLoading();
  }
}

// Display courses
function displayCourses(courses) {
  const coursesGrid = document.getElementById('coursesGrid');
  const noResults = document.getElementById('noResults');
  
  console.log('🎯 Displaying courses:', courses);
  
  if (!coursesGrid) {
    console.error('❌ coursesGrid element not found');
    return;
  }
  
  if (!courses || courses.length === 0) {
    coursesGrid.style.display = 'none';
    if (noResults) noResults.style.display = 'block';
    console.log('📭 No courses to display');
    return;
  }
  
  coursesGrid.style.display = 'grid';
  if (noResults) noResults.style.display = 'none';
  
  try {
    coursesGrid.innerHTML = courses.map(course => createCourseCard(course)).join('');
    console.log(`✅ Successfully displayed ${courses.length} courses`);
  } catch (error) {
    console.error('❌ Error creating course cards:', error);
    showError('حدث خطأ في عرض الدورات');
  }
}

// Create course card
function createCourseCard(course) {
  const categoryNames = {
    development: 'البرمجة والتطوير',
    business: 'الأعمال والإدارة',
    design: 'التصميم والجرافيك',
    marketing: 'التسويق الرقمي',
    languages: 'اللغات',
    other: 'أخرى'
  };
  
  const levelNames = {
    beginner: 'مبتدئ',
    intermediate: 'متوسط',
    advanced: 'متقدم'
  };
  
  const price = course.price > 0 ? `${course.price} ر.س` : 'مجاني';
  const rating = course.averageRating > 0 ? course.averageRating.toFixed(1) : 'جديد';
  const enrollmentCount = course.enrollmentCount || 0;
  
  // Enhanced video count handling
  const videoCount = course.videoCount || course.videos?.length || course.lessonsCount || 0;
  const videoText = getVideoCountText(videoCount);
  
  // Calculate total duration if available
  const totalDuration = course.totalDuration || calculateTotalDuration(course.videos);
  const durationText = totalDuration ? formatDuration(totalDuration) : '';
  
  return `
    <div class="course-card" onclick="viewCourse('${course._id}')">
      <div class="course-image">
        <img src="${course.thumbnail || '/uploads/no-photo.jpg'}" alt="${course.title}">
        <div class="course-level">${levelNames[course.level] || course.level}</div>
        <div class="course-price">${price}</div>
        ${videoCount > 0 ? `<div class="course-video-count"><i class="fas fa-play-circle"></i> ${videoCount}</div>` : ''}
      </div>
      
      <div class="course-content">
        <div class="course-category">${categoryNames[course.category] || course.category}</div>
        <h3 class="course-title">${course.title}</h3>
        <p class="course-description">${course.description}</p>
        
        <div class="course-instructor">
          <i class="fas fa-user"></i>
          <span>${course.instructor}</span>
        </div>
        
        <div class="course-meta">
          <div class="course-rating">
            <i class="fas fa-star"></i>
            <span>${rating}</span>
          </div>
          
          <div class="course-students">
            <i class="fas fa-users"></i>
            <span>${enrollmentCount} طالب</span>
          </div>
          
          <div class="course-videos">
            <i class="fas fa-play-circle"></i>
            <span>${videoText}</span>
            ${durationText ? `<small class="course-duration">${durationText}</small>` : ''}
          </div>
        </div>
      </div>
    </div>
  `;
}

// View course details
function viewCourse(courseId) {
  window.location.href = `/course-detail.html?id=${courseId}`;
}

// Perform search function
function performSearch() {
  const searchInput = document.getElementById('courseSearch');
  const searchValue = searchInput ? searchInput.value.trim() : '';
  
  if (searchValue === '') {
    loadAllCourses();
    return;
  }
  
  currentFilters.search = searchValue;
  currentPage = 1;
  
  console.log('🔍 Searching for:', searchValue);
  loadCourses(currentPage);
}

// Search courses (simplified)
function searchCourses() {
  performSearch();
}

// Filter courses based on all current filters
function filterCourses() {
  // Get current filter values
  const searchInput = document.getElementById('courseSearch');
  const categorySelect = document.getElementById('categoryFilter');
  const levelSelect = document.getElementById('levelFilter');
  const priceSelect = document.getElementById('priceFilter');
  const sortSelect = document.getElementById('sortFilter');

  // Update current filters
  currentFilters.search = searchInput ? searchInput.value.trim() : '';
  currentFilters.category = categorySelect ? categorySelect.value : '';
  currentFilters.level = levelSelect ? levelSelect.value : '';
  currentFilters.price = priceSelect ? priceSelect.value : '';
  currentFilters.sort = sortSelect ? sortSelect.value : '';

  // Reset to first page
  currentPage = 1;

  // Check if any filters are applied
  const hasFilters = currentFilters.search ||
                    currentFilters.category ||
                    currentFilters.level ||
                    currentFilters.price ||
                    currentFilters.sort;

  console.log('🔍 Applying filters:', currentFilters);
  console.log('📊 Has filters:', hasFilters);

  // Load courses with current filters
  loadCourses(currentPage);
}

// Reset all filters and show all courses
function resetFilters() {
  // Reset form elements
  const searchInput = document.getElementById('courseSearch');
  const categorySelect = document.getElementById('categoryFilter');
  const levelSelect = document.getElementById('levelFilter');
  const priceSelect = document.getElementById('priceFilter');
  const sortSelect = document.getElementById('sortFilter');

  if (searchInput) searchInput.value = '';
  if (categorySelect) categorySelect.value = '';
  if (levelSelect) levelSelect.value = '';
  if (priceSelect) priceSelect.value = '';
  if (sortSelect) sortSelect.value = '';

  // Reset current filters
  currentFilters = {
    search: '',
    category: '',
    level: '',
    price: '',
    sort: ''
  };

  // Reset page
  currentPage = 1;

  console.log('🔄 Filters reset, loading all courses');

  // Load all courses
  loadAllCourses();
}

// Sort courses
function sortCourses() {
  const sortFilter = document.getElementById('sortFilter');
  currentFilters.sort = sortFilter.value;
  currentPage = 1;
  
  loadCourses(currentPage);
}

// Display pagination
function displayPagination(pagination) {
  const paginationContainer = document.getElementById('coursesPagination');
  if (!paginationContainer || !pagination) return;
  
  const { page, pages, hasNext, hasPrev } = pagination;
  
  if (pages <= 1) {
    paginationContainer.innerHTML = '';
    return;
  }
  
  let paginationHTML = '<div class="pagination-controls">';
  
  // Previous button
  if (hasPrev) {
    paginationHTML += `<button class="pagination-btn" onclick="loadCourses(${page - 1})">
      <i class="fas fa-chevron-right"></i>
    </button>`;
  }
  
  // Page numbers
  const startPage = Math.max(1, page - 2);
  const endPage = Math.min(pages, page + 2);
  
  if (startPage > 1) {
    paginationHTML += `<button class="pagination-btn" onclick="loadCourses(1)">1</button>`;
    if (startPage > 2) {
      paginationHTML += '<span class="pagination-dots">...</span>';
    }
  }
  
  for (let i = startPage; i <= endPage; i++) {
    paginationHTML += `<button class="pagination-btn ${i === page ? 'active' : ''}" onclick="loadCourses(${i})">${i}</button>`;
  }
  
  if (endPage < pages) {
    if (endPage < pages - 1) {
      paginationHTML += '<span class="pagination-dots">...</span>';
    }
    paginationHTML += `<button class="pagination-btn" onclick="loadCourses(${pages})">${pages}</button>`;
  }
  
  // Next button
  if (hasNext) {
    paginationHTML += `<button class="pagination-btn" onclick="loadCourses(${page + 1})">
      <i class="fas fa-chevron-left"></i>
    </button>`;
  }
  
  paginationHTML += '</div>';
  paginationContainer.innerHTML = paginationHTML;
}

// Update search results message
function updateSearchResults(searchTerm, count, total) {
  const coursesCount = document.getElementById('coursesCount');
  if (!coursesCount) return;
  
  if (searchTerm) {
    if (count === 0) {
      coursesCount.innerHTML = `لا توجد نتائج لـ "<strong>${searchTerm}</strong>"`;
    } else if (count === 1) {
      coursesCount.innerHTML = `تم العثور على نتيجة واحدة لـ "<strong>${searchTerm}</strong>"`;
    } else {
      coursesCount.innerHTML = `تم العثور على <strong>${count}</strong> نتيجة لـ "<strong>${searchTerm}</strong>"`;
    }
  } else {
    updateResultsInfo(count, total);
  }
}

// Clear search
function clearSearch() {
  const searchInput = document.getElementById('courseSearch');
  if (searchInput) {
    searchInput.value = '';
  }
  currentFilters.search = '';
  currentPage = 1;
  loadAllCourses();
}

// Update courses count - fixed function name
function updateResultsInfo(count, total) {
  const coursesCount = document.getElementById('coursesCount');
  if (coursesCount) {
    if (count === total) {
      coursesCount.textContent = `${total} دورة`;
    } else {
      coursesCount.textContent = `${count} من ${total} دورة`;
    }
  }
}

// Update courses count
function updateCoursesCount(count, total) {
  // Only use updateResultsInfo if not searching
  if (!currentFilters.search) {
    updateResultsInfo(count, total);
  }
}

// Show loading
function showLoading() {
  const loading = document.getElementById('coursesLoading');
  const grid = document.getElementById('coursesGrid');
  const noResults = document.getElementById('noResults');
  
  if (loading) loading.style.display = 'block';
  if (grid) grid.style.display = 'none';
  if (noResults) noResults.style.display = 'none';
}

// Hide loading
function hideLoading() {
  const loading = document.getElementById('coursesLoading');
  if (loading) loading.style.display = 'none';
}

// Show error
function showError(message) {
  const noResults = document.getElementById('noResults');
  if (noResults) {
    noResults.innerHTML = `
      <i class="fas fa-exclamation-triangle"></i>
      <h3>حدث خطأ</h3>
      <p>${message}</p>
    `;
    noResults.style.display = 'block';
  }
}