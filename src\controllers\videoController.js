const Video = require('../models/Video');
const Course = require('../models/Course');
const { uploadVideo, uploadImage, deleteResource } = require('../config/cloudinary');
const asyncHandler = require('../middlewares/asyncHandler');
const config = require('../config/config');
const fs = require('fs');

// @desc    Get all videos
// @route   GET /api/videos
// @access  Public
exports.getVideos = asyncHandler(async (req, res) => {
  let query;

  // Check if course ID is provided
  if (req.params.courseId) {
    query = Video.find({ course: req.params.courseId }).sort('order');
  } else {
    query = Video.find().sort('order');
  }

  const videos = await query;

  res.status(200).json({
    success: true,
    count: videos.length,
    data: videos
  });
});

// @desc    Get single video
// @route   GET /api/videos/:id
// @access  Public
exports.getVideo = asyncHandler(async (req, res) => {
  const video = await Video.findById(req.params.id).populate({
    path: 'course',
    select: 'title description'
  });

  if (!video) {
    return res.status(404).json({
      success: false,
      message: `Video not found with id of ${req.params.id}`
    });
  }

  res.status(200).json({
    success: true,
    data: video
  });
});

// @desc    Create new video
// @route   POST /api/courses/:courseId/videos
// @access  Public
exports.createVideo = asyncHandler(async (req, res) => {
  console.log('🎬 Creating new video for course:', req.params.courseId);
  console.log('📝 Request body:', req.body);
  console.log('📁 Request files:', req.files);

  // Add course to req.body
  req.body.course = req.params.courseId;

  // Check if course exists
  const course = await Course.findById(req.params.courseId);

  if (!course) {
    return res.status(404).json({
      success: false,
      message: `Course not found with id of ${req.params.courseId}`
    });
  }

  // Make sure user is course owner or is admin
  if (course.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: `Not authorized to add videos to this course`
    });
  }

  // Check for uploaded files
  if (!req.files || !req.files.video || !req.files.video[0]) {
    return res.status(400).json({
      success: false,
      message: 'Please upload a video file'
    });
  }

  try {
    const videoFile = req.files.video[0];
    let thumbnailResult = null;

    console.log('📹 Video file info:', {
      filename: videoFile.filename,
      originalname: videoFile.originalname,
      mimetype: videoFile.mimetype,
      size: videoFile.size,
      path: videoFile.path
    });

    // Upload video to cloudinary
    console.log('☁️ Uploading video to Cloudinary...');
    const videoResult = await uploadVideo(videoFile.path, {
      folder: 'courses/videos',
      use_filename: true
    });
    console.log('✅ Video uploaded successfully:', videoResult.public_id);

    // Upload thumbnail if provided
    if (req.files.thumbnail && req.files.thumbnail[0]) {
      const thumbnailFile = req.files.thumbnail[0];
      thumbnailResult = await uploadImage(thumbnailFile.path, {
        folder: 'courses/video-thumbnails',
        use_filename: true
      });
    }

    // Create video with cloudinary data
    const video = await Video.create({
      ...req.body,
      videoUrl: videoResult.secure_url,
      videoId: videoResult.public_id,
      duration: videoResult.duration || 0,
      thumbnail: thumbnailResult ? thumbnailResult.secure_url : null,
      thumbnailId: thumbnailResult ? thumbnailResult.public_id : null
    });

    // Remove files from server
    fs.unlinkSync(videoFile.path);
    if (req.files.thumbnail && req.files.thumbnail[0]) {
      fs.unlinkSync(req.files.thumbnail[0].path);
    }

    res.status(201).json({
      success: true,
      data: video
    });
  } catch (error) {
    // Clean up uploaded files if video creation fails
    if (req.files.video && req.files.video[0] && fs.existsSync(req.files.video[0].path)) {
      fs.unlinkSync(req.files.video[0].path);
    }
    if (req.files.thumbnail && req.files.thumbnail[0] && fs.existsSync(req.files.thumbnail[0].path)) {
      fs.unlinkSync(req.files.thumbnail[0].path);
    }
    throw error;
  }

});

// @desc    Update video
// @route   PUT /api/videos/:id
// @access  Public
exports.updateVideo = asyncHandler(async (req, res) => {
  let video = await Video.findById(req.params.id).populate('course');

  if (!video) {
    return res.status(404).json({
      success: false,
      message: `Video not found with id of ${req.params.id}`
    });
  }

  // Make sure user is course owner or is admin
  if (video.course.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: `Not authorized to update this video`
    });
  }

  let videoData = {};

  try {
    // Upload new video to cloudinary if it exists
    if (req.file) {
      // Delete previous video from cloudinary if exists
      if (video.videoId) {
        await deleteResource(video.videoId, 'video');
      }

      // Upload new video
      const result = await uploadVideo(req.file.path, {
        folder: 'courses/videos',
        use_filename: true
      });

      // Add cloudinary url and id to video
      videoData = {
        videoUrl: result.secure_url,
        videoId: result.public_id,
        duration: result.duration || 0
      };

      // Remove file from server
      fs.unlinkSync(req.file.path);
    }
  } catch (error) {
    // Clean up uploaded file if update fails
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    throw error;
  }

    video = await Video.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        ...videoData
      },
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: video
    });
});

// @desc    Delete video
// @route   DELETE /api/videos/:id
// @access  Public
exports.deleteVideo = asyncHandler(async (req, res) => {
  const video = await Video.findById(req.params.id).populate('course');

  if (!video) {
    return res.status(404).json({
      success: false,
      message: `Video not found with id of ${req.params.id}`
    });
  }

  // Make sure user is course owner or is admin
  if (video.course.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: `Not authorized to delete this video`
    });
  }

  // Delete video from cloudinary if exists
  if (video.videoId) {
    await deleteResource(video.videoId, 'video');
  }

  await video.deleteOne();

  res.status(200).json({
    success: true,
    data: {}
  });
});