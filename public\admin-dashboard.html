<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>لوحة تحكم الإدارة - منصة دوراتي</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin-dashboard.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <a href="/admin-dashboard.html" class="navbar-brand">
        <i class="fas fa-graduation-cap"></i>
        منصة دوراتي
      </a>
      
      <div class="nav-user">
        <div class="user-info">
          <img id="navProfileImage" src="/uploads/no-photo.jpg" class="nav-profile-image" alt="الصورة الشخصية">
          <div class="user-details">
            <span id="navUserName">مرحباً</span>
            <small id="navUserRole">مدير</small>
          </div>
        </div>
        <div class="nav-actions">
          <a href="/profile.html" class="nav-link">
            <i class="fas fa-user"></i>
            الملف الشخصي
          </a>
          <button id="logoutBtn" class="nav-link logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            تسجيل الخروج
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Container -->
  <div class="admin-container">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h3>لوحة التحكم</h3>
      </div>
      
      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <a href="#dashboard" class="nav-link active" data-section="dashboard">
              <i class="fas fa-chart-line"></i>
              <span>نظرة عامة</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#users" class="nav-link" data-section="users">
              <i class="fas fa-users"></i>
              <span>إدارة المستخدمين</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#courses" class="nav-link" data-section="courses">
              <i class="fas fa-book"></i>
              <span>إدارة الدورات</span>
            </a>
          </li>
 
        </ul>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Dashboard Section -->
      <section id="dashboard-section" class="content-section active">
        <div class="section-header">
          <h2>نظرة عامة</h2>
          <p>مرحباً بك في لوحة تحكم الإدارة</p>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3 id="totalUsers">0</h3>
              <p>إجمالي المستخدمين</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-book"></i>
            </div>
            <div class="stat-content">
              <h3 id="totalCourses">0</h3>
              <p>إجمالي الدورات</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
              <h3 id="totalEnrollments">0</h3>
              <p>إجمالي التسجيلات</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-lock"></i>
            </div>
            <div class="stat-content">
              <h3 id="lockedUsers">0</h3>
              <p>الحسابات المقفلة</p>
            </div>
          </div>
        </div>

        <div class="recent-activity">
          <div class="activity-header">
            <h3>النشاط الأخير</h3>
            <div class="activity-actions">
              <button class="btn btn-sm btn-outline-primary" onclick="loadRecentActivities()">
                <i class="fas fa-refresh"></i>
                تحديث
              </button>
              <button class="btn btn-sm btn-outline-secondary" onclick="viewAllActivities()">
                <i class="fas fa-list"></i>
                عرض الكل
              </button>
            </div>
          </div>
          <div class="activity-list" id="recentActivity">
            <!-- Activity items will be loaded here -->
            <div class="activity-loading">
              <div class="spinner"></div>
              <p>جاري تحميل النشاط الأخير...</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Users Management Section -->
      <section id="users-section" class="content-section">
        <div class="section-header">
          <h2>إدارة المستخدمين</h2>
          <div class="section-actions">
            <div class="search-box">
              <input type="text" id="userSearch" placeholder="البحث عن مستخدم...">
              <i class="fas fa-search"></i>
            </div>
            <select id="userRoleFilter">
              <option value="">جميع الأدوار</option>
              <option value="user">مستخدم</option>
              <option value="admin">مدير</option>
            </select>
            <select id="userStatusFilter">
              <option value="">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
              <option value="locked">مقفل</option>
            </select>
            <button class="btn btn-primary" onclick="openAddUserModal()">
              <i class="fas fa-user-plus"></i>
              إضافة مستخدم جديد
            </button>
          </div>
        </div>

        <div class="table-container">
          <table class="users-table">
            <thead>
              <tr>
                <th>الصورة</th>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>الدور</th>
                <th>الحالة</th>
                <th>تاريخ التسجيل</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
              <!-- Users will be loaded here -->
            </tbody>
          </table>
        </div>

        <div class="pagination" id="usersPagination">
          <!-- Pagination will be loaded here -->
        </div>
      </section>

      <!-- Courses Management Section -->
      <section id="courses-section" class="content-section">
        <div class="section-header">
          <h2>إدارة الدورات</h2>
          <div class="section-actions">
            <div class="search-box">
              <input type="text" id="courseSearch" placeholder="البحث عن دورة...">
              <i class="fas fa-search"></i>
            </div>
            <select id="courseCategoryFilter">
              <option value="">جميع التصنيفات</option>
              <option value="development">البرمجة والتطوير</option>
              <option value="business">الأعمال والإدارة</option>
              <option value="design">التصميم والجرافيك</option>
              <option value="marketing">التسويق الرقمي</option>
              <option value="languages">اللغات</option>
              <option value="other">أخرى</option>
            </select>
            <select id="courseStatusFilter">
              <option value="">جميع الحالات</option>
              <option value="true">منشورة</option>
              <option value="false">مسودة</option>
            </select>
            <button class="btn btn-primary" onclick="openAddCourseModal()">
              <i class="fas fa-plus"></i>
              إضافة دورة جديدة
            </button>
          </div>
        </div>

        <div class="courses-grid" id="coursesGrid">
          <!-- Courses will be loaded here -->
        </div>

        <div class="pagination" id="coursesPagination">
          <!-- Pagination will be loaded here -->
        </div>
      </section>

 

  
    </main>
  </div>

  <!-- Add Course Modal -->
  <div id="addCourseModal" class="modal">
    <div class="modal-content modal-large">
      <div class="modal-header">
        <h3>إضافة دورة جديدة</h3>
        <span class="close" onclick="closeAddCourseModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="addCourseForm" enctype="multipart/form-data">
          <div class="form-row">
            <div class="form-group">
              <label for="addCourseTitle">عنوان الدورة</label>
              <input type="text" id="addCourseTitle" placeholder="أدخل عنوان الدورة" required maxlength="100">
            </div>

            <div class="form-group">
              <label for="addCourseInstructor">اسم المدرب</label>
              <input type="text" id="addCourseInstructor" placeholder="أدخل اسم المدرب" required>
            </div>
          </div>

          <div class="form-group">
            <label for="addCourseDescription">وصف الدورة</label>
            <textarea id="addCourseDescription" placeholder="أدخل وصف الدورة" required maxlength="500" rows="4"></textarea>
            <small class="form-text">الحد الأقصى 500 حرف</small>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="addCourseCategory">التصنيف</label>
              <select id="addCourseCategory" required>
                <option value="">اختر التصنيف</option>
                <option value="development">البرمجة والتطوير</option>
                <option value="business">الأعمال والإدارة</option>
                <option value="design">التصميم والجرافيك</option>
                <option value="marketing">التسويق الرقمي</option>
                <option value="languages">اللغات</option>
                <option value="other">أخرى</option>
              </select>
            </div>

            <div class="form-group">
              <label for="addCourseLevel">المستوى</label>
              <select id="addCourseLevel" required>
                <option value="">اختر المستوى</option>
                <option value="beginner">مبتدئ</option>
                <option value="intermediate">متوسط</option>
                <option value="advanced">متقدم</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="addCoursePrice">السعر (ريال سعودي)</label>
              <input type="number" id="addCoursePrice" placeholder="0" min="0" step="0.01">
              <small class="form-text">اتركه فارغاً أو 0 للدورات المجانية</small>
            </div>

            <div class="form-group">
              <label for="addCourseStatus">حالة النشر</label>
              <select id="addCourseStatus">
                <option value="false">مسودة</option>
                <option value="true">منشورة</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="addCourseThumbnail">صورة الدورة</label>
            <input type="file" id="addCourseThumbnail" accept="image/*">
            <small class="form-text">اختياري - يفضل صورة بحجم 1280x720 بكسل</small>
          </div>

          <div class="form-group">
            <label for="addCourseTags">الكلمات المفتاحية</label>
            <input type="text" id="addCourseTags" placeholder="مثال: JavaScript, React, تطوير ويب">
            <small class="form-text">افصل بين الكلمات بفاصلة</small>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-plus"></i>
              إضافة الدورة
            </button>
            <button type="button" class="btn btn-secondary" onclick="closeAddCourseModal()">
              <i class="fas fa-times"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add User Modal -->
  <div id="addUserModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>إضافة مستخدم جديد</h3>
        <span class="close" onclick="closeAddUserModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="addUserForm">
          <div class="form-group">
            <label for="addUserName">الاسم الكامل</label>
            <input type="text" id="addUserName" placeholder="أدخل الاسم الكامل" required>
          </div>

          <div class="form-group">
            <label for="addUserEmail">البريد الإلكتروني</label>
            <input type="email" id="addUserEmail" placeholder="أدخل البريد الإلكتروني" required>
          </div>

          <div class="form-group">
            <label for="addUserPassword">كلمة المرور</label>
            <input type="password" id="addUserPassword" placeholder="أدخل كلمة المرور" required minlength="6">
            <small class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
          </div>

          <div class="form-group">
            <label for="addUserConfirmPassword">تأكيد كلمة المرور</label>
            <input type="password" id="addUserConfirmPassword" placeholder="أعد إدخال كلمة المرور" required>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="addUserRole">الدور</label>
              <select id="addUserRole" required>
                <option value="">اختر الدور</option>
                <option value="user">مستخدم</option>
                <option value="admin">مدير</option>
              </select>
            </div>

            <div class="form-group">
              <label for="addUserStatus">الحالة</label>
              <select id="addUserStatus">
                <option value="true">نشط</option>
                <option value="false">غير نشط</option>
              </select>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-user-plus"></i>
              إضافة المستخدم
            </button>
            <button type="button" class="btn btn-secondary" onclick="closeAddUserModal()">
              <i class="fas fa-times"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- User Edit Modal -->
  <div id="userEditModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>تعديل المستخدم</h3>
        <span class="close" onclick="closeUserEditModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="userEditForm">
          <input type="hidden" id="editUserId">

          <div class="form-group">
            <label for="editUserName">الاسم</label>
            <input type="text" id="editUserName" required>
          </div>

          <div class="form-group">
            <label for="editUserEmail">البريد الإلكتروني</label>
            <input type="email" id="editUserEmail" required>
          </div>

          <div class="form-group">
            <label for="editUserRole">الدور</label>
            <select id="editUserRole">
              <option value="user">مستخدم</option>
              <option value="admin">مدير</option>
            </select>
          </div>

          <div class="form-group">
            <label for="editUserStatus">الحالة</label>
            <select id="editUserStatus">
              <option value="true">نشط</option>
              <option value="false">غير نشط</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            <button type="button" class="btn btn-secondary" onclick="closeUserEditModal()">إلغاء</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Course Edit Modal -->
  <div id="courseEditModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>تعديل الدورة</h3>
        <span class="close" onclick="closeCourseEditModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="courseEditForm">
          <input type="hidden" id="editCourseId">

          <div class="form-group">
            <label for="editCourseTitle">عنوان الدورة</label>
            <input type="text" id="editCourseTitle" required>
          </div>

          <div class="form-group">
            <label for="editCourseDescription">وصف الدورة</label>
            <textarea id="editCourseDescription" rows="4" required></textarea>
          </div>

          <div class="form-group">
            <label for="editCourseInstructor">المدرب</label>
            <input type="text" id="editCourseInstructor" required>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="editCourseCategory">التصنيف</label>
              <select id="editCourseCategory" required>
                <option value="">اختر التصنيف</option>
                <option value="development">البرمجة والتطوير</option>
                <option value="business">الأعمال والإدارة</option>
                <option value="design">التصميم والجرافيك</option>
                <option value="marketing">التسويق الرقمي</option>
                <option value="languages">اللغات</option>
                <option value="other">أخرى</option>
              </select>
            </div>

            <div class="form-group">
              <label for="editCourseLevel">المستوى</label>
              <select id="editCourseLevel" required>
                <option value="">اختر المستوى</option>
                <option value="beginner">مبتدئ</option>
                <option value="intermediate">متوسط</option>
                <option value="advanced">متقدم</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="editCoursePrice">السعر (دولار)</label>
              <input type="number" id="editCoursePrice" min="0" step="0.01">
            </div>

            <div class="form-group">
              <label for="editCourseStatus">الحالة</label>
              <select id="editCourseStatus">
                <option value="true">منشورة</option>
                <option value="false">مسودة</option>
              </select>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            <button type="button" class="btn btn-secondary" onclick="closeCourseEditModal()">إلغاء</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Course Detail Modal -->
  <div id="courseDetailModal" class="modal">
    <div class="modal-content modal-large">
      <div class="modal-header">
        <h3>تفاصيل الدورة</h3>
        <span class="close" onclick="closeCourseDetailModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div id="courseDetailContent">
          <!-- Course details will be loaded here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Add Video Modal -->
  <div id="addVideoModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>إضافة فيديو جديد</h3>
        <span class="close" onclick="closeAddVideoModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="addVideoForm" enctype="multipart/form-data">
          <input type="hidden" id="addVideoCourseId">

          <div class="form-group">
            <label for="addVideoTitle">عنوان الفيديو</label>
            <input type="text" id="addVideoTitle" placeholder="أدخل عنوان الفيديو" required maxlength="100">
          </div>

          <div class="form-group">
            <label for="addVideoDescription">وصف الفيديو</label>
            <textarea id="addVideoDescription" placeholder="أدخل وصف الفيديو" required maxlength="500" rows="3"></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="addVideoFile">ملف الفيديو</label>
              <input type="file" id="addVideoFile" accept="video/*" required>
              <small class="form-text">الحد الأقصى 500 ميجابايت</small>
            </div>

            <div class="form-group">
              <label for="addVideoOrder">ترتيب الفيديو</label>
              <input type="number" id="addVideoOrder" placeholder="1" min="1" value="1">
            </div>
          </div>

          <div class="form-group">
            <label for="addVideoThumbnail">صورة مصغرة للفيديو</label>
            <input type="file" id="addVideoThumbnail" accept="image/*">
            <small class="form-text">اختياري - سيتم إنشاؤها تلقائياً إذا لم تُحدد</small>
          </div>

          <div class="form-group">
            <label for="addVideoStatus">حالة النشر</label>
            <select id="addVideoStatus">
              <option value="true">منشور</option>
              <option value="false">مسودة</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-video"></i>
              إضافة الفيديو
            </button>
            <button type="button" class="btn btn-secondary" onclick="closeAddVideoModal()">
              <i class="fas fa-times"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div id="loadingSpinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Scripts -->
  <script src="/js/auth.js"></script>
  <script src="/js/admin-dashboard.js"></script>
</body>
</html>
